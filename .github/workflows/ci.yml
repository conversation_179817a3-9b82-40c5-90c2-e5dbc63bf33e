name: CI

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

# Cancel in-progress runs on new commits to same PR
concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  setup:
    name: setup
    runs-on: ubuntu-latest
    timeout-minutes: 15
    outputs:
      nx-shas: ${{ steps.set-shas.outputs.base-and-head }}
      affected: ${{ steps.affected.outputs.affected }}
      affected_list: ${{ steps.affected.outputs.affected_list }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          path: . # Checkout to the current GITHUB_WORKSPACE

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 'v22.13.1'
          cache: 'npm'
          cache-dependency-path: |
            package-lock.json

      - name: Install root dependencies
        run: npm ci

      - name: Install codashi dependencies
        run: |
          cd apps/codashi
          npm ci

      - name: Derive appropriate SHAs for base and head for `nx affected` commands
        id: set-shas
        uses: nrwl/nx-set-shas@v4
        with:
          main-branch-name: 'main'

      # Check if we have any affected projects
      - name: Check for affected projects
        id: affected
        run: |
          affected_projects=$(npx nx show projects --affected --json)
          echo "affected=$(echo "$affected_projects" | jq -r '. | length > 0')" >> $GITHUB_OUTPUT
          echo "affected_list=$(echo "$affected_projects" | jq -r 'join(",")')" >> $GITHUB_OUTPUT

      - name: Cache Nx
        uses: actions/cache@v3
        with:
          path: |
            node_modules/.cache/nx
            .nx/cache
          key: nx-${{ runner.os }}-${{ hashFiles('package-lock.json') }}-${{ github.sha }}
          restore-keys: |
            nx-${{ runner.os }}-${{ hashFiles('package-lock.json') }}-
            nx-${{ runner.os }}-

      - name: Cache repo
        uses: actions/cache/save@v3
        with:
          path: .
          key: repo-${{ github.sha }}

  format:
    name: format
    needs: setup
    runs-on: ubuntu-latest
    timeout-minutes: 10
    if: needs.setup.outputs.affected == 'true'
    steps:
      - name: Restore repo
        uses: actions/cache/restore@v3
        with:
          path: .
          key: repo-${{ github.sha }}
          fail-on-cache-miss: true

      - name: Restore Nx cache
        uses: actions/cache/restore@v3
        with:
          path: |
            node_modules/.cache/nx
            .nx/cache
          key: nx-${{ runner.os }}-${{ hashFiles('package-lock.json') }}-${{ github.sha }}
          restore-keys: |
            nx-${{ runner.os }}-${{ hashFiles('package-lock.json') }}-
            nx-${{ runner.os }}-

      - run: npx nx format:check --base=origin/main --head=${GITHUB_SHA}

  lint:
    name: lint
    needs: setup
    runs-on: ubuntu-latest
    timeout-minutes: 10
    if: needs.setup.outputs.affected == 'true'
    steps:
      - name: Restore repo
        uses: actions/cache/restore@v3
        with:
          path: .
          key: repo-${{ github.sha }}
          fail-on-cache-miss: true

      - name: Restore Nx cache
        uses: actions/cache/restore@v3
        with:
          path: |
            node_modules/.cache/nx
            .nx/cache
          key: nx-${{ runner.os }}-${{ hashFiles('package-lock.json') }}-${{ github.sha }}
          restore-keys: |
            nx-${{ runner.os }}-${{ hashFiles('package-lock.json') }}-
            nx-${{ runner.os }}-

      - run: npx nx affected:lint --base=origin/main --head=${GITHUB_SHA}

  build:
    name: build
    needs: setup
    runs-on: ubuntu-latest
    timeout-minutes: 30
    if: needs.setup.outputs.affected == 'true'
    steps:
      - name: Restore repo
        uses: actions/cache/restore@v3
        with:
          path: .
          key: repo-${{ github.sha }}
          fail-on-cache-miss: true

      - name: Restore Nx cache
        uses: actions/cache/restore@v3
        with:
          path: |
            node_modules/.cache/nx
            .nx/cache
          key: nx-${{ runner.os }}-${{ hashFiles('package-lock.json') }}-${{ github.sha }}
          restore-keys: |
            nx-${{ runner.os }}-${{ hashFiles('package-lock.json') }}-
            nx-${{ runner.os }}-

      - run: npx nx affected:build --base=origin/main --head=${GITHUB_SHA}

  test:
    name: test
    needs: setup
    runs-on: ubuntu-latest
    timeout-minutes: 30
    if: needs.setup.outputs.affected == 'true'
    strategy:
      matrix:
        shard: [1, 2]
      fail-fast: false
    steps:
      - name: Restore repo
        uses: actions/cache/restore@v3
        with:
          path: .
          key: repo-${{ github.sha }}
          fail-on-cache-miss: true

      - name: Restore Nx cache
        uses: actions/cache/restore@v3
        with:
          path: |
            node_modules/.cache/nx
            .nx/cache
          key: nx-${{ runner.os }}-${{ hashFiles('package-lock.json') }}-${{ github.sha }}
          restore-keys: |
            nx-${{ runner.os }}-${{ hashFiles('package-lock.json') }}-
            nx-${{ runner.os }}-

      - run: npx nx affected:test --base=origin/main --head=${GITHUB_SHA} --shard=${{ matrix.shard }}/2

  browser-tests:
    name: browser-tests
    needs: setup
    runs-on: ubuntu-latest
    timeout-minutes: 30
    if: needs.setup.outputs.affected == 'true' && contains(needs.setup.outputs.affected_list, 'codashi')
    steps:
      - name: Restore repo
        uses: actions/cache/restore@v3
        with:
          path: .
          key: repo-${{ github.sha }}
          fail-on-cache-miss: true

      - name: Restore Nx cache
        uses: actions/cache/restore@v3
        with:
          path: |
            node_modules/.cache/nx
            .nx/cache
          key: nx-${{ runner.os }}-${{ hashFiles('package-lock.json') }}-${{ github.sha }}
          restore-keys: |
            nx-${{ runner.os }}-${{ hashFiles('package-lock.json') }}-
            nx-${{ runner.os }}-

      - name: Install Playwright browsers for codashi
        working-directory: apps/codashi
        run: npx playwright install --with-deps chromium

      - name: Verify Playwright installation
        working-directory: apps/codashi
        run: |
          npx playwright --version
          echo "Checking for local browsers..."
          ls -la ./node_modules/playwright-core/.local-browsers/ || echo "Local browsers not found"
          echo "Checking global browsers..."
          ls -la ~/.cache/ms-playwright/ || echo "Global browsers not found"

      - name: Run browser tests
        working-directory: apps/codashi
        run: npx vitest run --config=vitest.browser.ci.config.ts
        env:
          CI: true

      - name: Upload browser test artifacts
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: browser-test-results
          path: |
            apps/codashi/test-results/
            apps/codashi/playwright-report/
          retention-days: 7
