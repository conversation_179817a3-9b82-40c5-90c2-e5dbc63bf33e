{"name": "@awe-labs/source", "version": "0.0.0", "license": "MIT", "type": "commonjs", "scripts": {"migrate-core-api": "ts-node ./apis/core/src/run-migrations.ts", "typecheck": "npx nx run-many --target=typecheck --all --watch", "lint": "npx nx run-many --target=lint --all", "test": "npx nx run-many --target=test --all", "build": "npx nx run-many --target=build --all", "format:write": "npx nx format:write", "coda-hq:start": "npx nx serve codarashi-hq"}, "private": true, "dependencies": {"@cloudflare/ai": "^1.2.2", "@codemirror/lint": "^6.8.5", "@codemirror/view": "^6.36.8", "@fontsource-variable/jetbrains-mono": "^5.0.20", "@fontsource-variable/merriweather-sans": "^5.0.13", "@fontsource/merriweather": "^5.0.12", "@js-temporal/polyfill": "^0.5.1", "@langchain/cloudflare": "^0.1.0", "@langchain/core": "^0.3.57", "@langchain/mistralai": "^0.2.1", "@react-pdf/renderer": "^4.3.0", "@supercharge/promise-pool": "^3.2.0", "@uidotdev/usehooks": "^2.4.1", "@uiw/codemirror-extensions-basic-setup": "^4.23.12", "@uiw/codemirror-extensions-langs": "^4.23.12", "@uiw/codemirror-theme-dracula": "^4.23.12", "@uiw/react-codemirror": "^4.23.12", "clsx": "^2.1.1", "drizzle-orm": "^0.38.3", "isbot": "^3.6.8", "langchain": "^0.3.27", "nanoid": "^5.0.6", "p-retry": "^6.2.1", "rambda": "^10.3.2", "react": "18.3.1", "react-dom": "18.3.1", "react-hook-form": "^7.51.3", "react-icons": "^5.0.1", "tesseract.js": "^6.0.1", "ts-pattern": "^5.5.0", "tslib": "^2.3.0", "type-fest": "^4.41.0", "unpdf": "^1.0.5", "yaml": "^2.8.0", "zod": "^3.25.20"}, "devDependencies": {"@nx/devkit": "21.1.1", "@nx/esbuild": "21.1.1", "@nx/eslint": "21.1.1", "@nx/eslint-plugin": "21.1.1", "@nx/js": "21.1.2", "@nx/playwright": "21.1.1", "@nx/react": "^21.1.2", "@nx/storybook": "^21.1.2", "@nx/vite": "21.1.1", "@nx/web": "21.1.2", "@nx/workspace": "21.1.1", "@playwright/test": "^1.49.1", "@storybook/addon-essentials": "^8.6.11", "@storybook/core-server": "^8.6.11", "@storybook/react-vite": "^8.6.11", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "16.1.0", "@testing-library/user-event": "^14.5.1", "@types/node": "^20.0.0", "@types/pg": "^8.10.9", "@types/react": "18.3.1", "@types/react-dom": "18.3.1", "@typescript-eslint/eslint-plugin": "7.18.0", "@typescript-eslint/parser": "7.18.0", "@vitejs/plugin-react-swc": "^3.5.0", "@vitest/browser": "^1.6.1", "@vitest/coverage-v8": "^1.0.4", "@vitest/ui": "^1.3.1", "blob-polyfill": "^9.0.20240710", "drizzle-kit": "^0.30.1", "esbuild": "^0.19.2", "eslint": "8.57.0", "eslint-config-prettier": "10.1.5", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.1", "eslint-plugin-playwright": "^0.15.3", "eslint-plugin-react": "7.35.0", "eslint-plugin-react-hooks": "5.0.0", "jiti": "2.4.2", "jsdom": "^26.1.0", "nx": "21.1.1", "prettier": "^2.6.2", "storybook": "^8.6.11", "ts-node": "10.9.1", "tsx": "^4.19.2", "typescript": "5.7.3", "vite": "6.3.5", "vitest": "1.6.1"}}