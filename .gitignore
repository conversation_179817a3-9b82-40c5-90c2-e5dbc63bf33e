# See http://help.github.com/ignore-files/ for more about ignoring files.

# compiled output
dist
tmp
/out-tsc

# dependencies
node_modules

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# misc
/.sass-cache
/connect.lock
/coverage
/libpeerconnection.log
npm-debug.log
yarn-error.log
testem.log
/typings

# System Files
.DS_Store
Thumbs.db

.nx/cache
.nx/workspace-data

# Next.js
.next
out

vite.config.*.timestamp*
vitest.config.*.timestamp*

# Cloudflare Workers
typed_workers/worker-configuration.d.ts
worker-configuration.d.ts



.cursor/rules/nx-rules.mdc
.github/instructions/nx.instructions.md
apps/codashi/worker-configuration.d.ts
apps/codashi/.env
ai-assets/data/memory.jsonl
