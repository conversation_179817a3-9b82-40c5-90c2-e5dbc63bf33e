# TypeScript Guidelines

## Type Definitions

- Always use `type` instead of `interface`
- Use `import { type SomeType }` when importing types
- If an explicit return type annotation is not reused elsewhere, omit it
- Favor union types over optional properties for increased type safety
- Use discriminated unions for result types with success/failure states

## Functions

- Use function expressions instead of function declarations:

  ```typescript
  // good
  const myFunction = () => {
    // ...
  };

  // bad
  function myFunction() {
    // ...
  }
  ```

- If a function or method's arguments exceed 3, refactor them into an object

## Enums

- Avoid native `enum` - simulate them instead:

  ```typescript
  // good
  const SomeEnum = {
    Value1: 'value1' as const,
    Value2: 'value2' as const,
  } as const;

  type SomeEnum = (typeof SomeEnum)[keyof typeof SomeEnum];

  // bad
  enum SomeEnum {
    Value1 = 'value1',
    Value2 = 'value2',
  }
  ```

## Type Safety & Validation

1. **Prefer Strict Types**

   - Define precise types for all inputs/outputs
   - Use utility types (`Partial`, `Pick`, etc.) for complex scenarios. the `@awe/core` library could have useful helpers along with the `type-fest` library
   - Leverage branded types for domain-specific constraints
   - Use zod parsers and derive types from them for all inputs and outputs
   - Use the `zodToDomainOrThrow` function to convert from zod to domain types

2. **Type Casting & Any Usage**

   - Avoid type assertions - prefer type inference and proper typing
   - Never use `any` unless absolutely unavoidable - use `unknown` with type guards instead
   - When forced to use type assertions, add a `// @ts-expect-error` comment explaining why

3. **Pattern Matching**
   - Use the `ts-pattern` library for highly type-safe pattern matching instead of switch statements

## Error Handling

- Use custom error types from `@awe/core`:
  - `domainError` for business logic/domain-related errors
  - `parsingError` for data transformation/parsing errors
  - `unexpectedError` for unexpected/system errors
- Never throw generic Error objects
