We have the following core error classes exported from `@awe/core`:

## Core error classes, shared across the monorepo and different application layers

- `ParsingError` - used for parsing error, usually that involves `zod` and is done at the application's boundaries - endpoints, reads from the database, upon receiving data from a network request or in other words - each place where the incoming data might have malformed shape.
- `DomainError` - use this for predictable/expected error situations, for core business logic errors. Feel free to extend the `ErrorMessages` enum that it uses but do try to keep it higher level and more general.
- `UnexpectedError` - used for unexpected errors, library failures and anything which isn't a core business logic error.

- whenever possible try to add more context to the errors in a way that would make it easier to understand or debug. They support passing the context either as a 2nd argument in the constructor or through a dedicated `addContext` method.

## Internal error classes

Whenever tackling a more complex unit of the application it could make sense to create custom error classes and keep them private so that we don't pollute the core error classes. Just make sure that to convert them into a core error once they leave the current abstraction layer. Example of this:

```
1. an endpoint controllers calls an internal service for some nuanced operation
2. the service throws a custom error
3. the controller catches the error and converts it into a core error before returning it to the client
```
