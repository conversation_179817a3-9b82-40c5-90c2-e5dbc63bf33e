- we're using react-router-7 and should generally foolow its idiomatic patterns. If unsure - do a web search or use context7 mcp.
- we're not using file-based routing and instead keep all our routes in the code usually in a `routes.ts` file
- we're using `type Props = {}` as name unless there will be a name conflict. The reason is to keep noise levels low
- we're declaring react components as `const SomeComponent: FC<Props> = () => { ... }`
- we're using custom hooks whenever we need to encapsulate more complex logic and keep the component itself cleaner
