### Creating and iterating on the plan

- use the `./plans/example-plan.md` to deduct the expected structure and format of the planning doc. You MUST create a doc and name it either at your assumption or use any user-provided hint unless you're continuing work on existing doc and in that case you MUST continue the work from wherever the progress in that doc is at.
- you're highly encouraged to ask follow up questions from the human and refine the plan as much as possible so that AI can do the coding with it later with ease and with as little effort/ambiguity as possible.

### Execution of the plan

- report which task you're working on if possible in the cli or other applicable way
- tick the checkboxes whenever you complete a task, feel free to leave any additional comments if needed, e.g. - notes about how you resolved it, anything we should consider and so on. Including if you failed to complete a step - do leave any helpful tips for the human reviewing your work.
- do not write new tests unless explicitly prompted to do so while creating the planning doc
- do try to fix any existing tests we break with our new work, but if it proves hard - limit it to just a few retries and give up. Leave a note for help in the planning doc or/and respond to the human, include any useful info about the issue. Do ask follow up clarifying questions if you believe this can help.
- do diagnostics at checkpoints that make sense (e.g. - before ticking a task from the list), read the `diagnostics.md` in `/ai-assets` for guidelines.

### After we're done implementing

- analyze whether we need to update the root `/ai-assets` directory and any file inside. Maybe we introduced a new pattern - we should keep the doc files up to date.
