# Project Overview

**AWE Labs Monorepo**

This NX repository may contain various apps and libs within different business domains, located in the said folders.

They all do follow the same coding guidelines and are written in typescript and tools from its ecosystem. Individual projects run their commands through `npx nx`.

**Primary Technologies:**

- Nx
- TypeScript
- React
- React-router-7 in framework mode
- semantic and modern html and css features

# Monorepo Structure

## Active Projects

### Applications

- **codashi** (`apps/codashi`)

  Current production system combining resume analysis, job matching, and job hunt tracking tools for software engineers.

  Tech stack intention:

  react-router 7 in framework mode
  react-pdf
  react-email + resend
  zod
  modern vanilla css, both with semantic and utility classes
  postgres, drizzle
  langchain.js
  runs within cloudflare workers
  uses utilities from rambda

### Libraries

- **awe-core** (`libs/core`)

  Isomorphic core library for all AWE projects, containing shared types, utilities, and error classes

- **codashi-core** (`libs/codashi-core`)

  Core business logic for resume parsing, skill matching, and job recommendation algorithms, accompanying the `codashi` project.

- **codashi-ui** (`libs/codashi-ui`)

  Shared React UI components, helpers and design system for the `codashi` project.

### node_modules and dependency management

- the intent is to put as many deps as possible at the root `package.json` and `node_modules` and only leave project-specific ones inside the respective `apps/{project-name}` folder.
- this is done to avoid conflicts and to make it easier to manage dependencies across projects
