# Summary

I want to build a resume builder for software engineers with AI capabilities.

It's for power users and large portion of the editing experience is through special yaml syntax which then translates to js objects using zod schemas and then render it in react-pdf.

For the code editor I would like to use `codemirror` with its `yaml` capabilities. My plan is to offer top-notch autocomplete, suggestions and such, trying to get close to modern AI editors like windsurf or cursor, just for much much smaller niche of possible values to enter and deal with.

My UI plan is to have a largely chat driven application with the chat window on the left where we put our PDF resume (initially), paste job descriptions or trigger other actions in the app through natural language. On the right we have the code editor with the yaml code and a PDF preview or other piece of UI which were brought up by the AI.
