# General tips

- we're putting extreme effort into type safety and code quality - you must read and follow the `guidelines/typescript.md` file before anything else!
- use snake_case for fields in JSON schemas or database fields. camelCase for regular code variables
- use kebab-case for file and folder names
- use custom error classes when designing internal logic and services. For boundary-level surfaces like an endpoint or react-router action errors (program inputs and outputs) - return one of my custom errors from @awe/core - ParsingError, DomainError, UnexpectedError. You can check `guideliness/errors.md` for more details.
- if a function or method's arguments become more than 3 - refactor them into an object instead
- try to follow good separation of concerns and single responsibility principle
- it's fine to introduce code duplication in cases when it makes the logic simpler, DRY shouldn't be at all costs - only when it makes sense

---

# Engineering Best Practices

## Validation Strategy

1. **Runtime Validation**  
   Only add runtime validation when:

   - Handling external/untrusted input (APIs, user input, etc.)
   - Dealing with type boundaries (e.g., API endpoints)
   - Checking complex invariants that can't be expressed in types

2. **Validation Layers**
   - Validate all external data at the boundaries of your system
   - Keep internal functions free of redundant validation when types are already guaranteed

## Example: Input Processing

```typescript
// At system boundary (e.g., API endpoint)
const processRequest = (rawInput: unknown) => {
  const input = validateInput(rawInput); // Validate external input
  return processWithGuaranteedTypes(input); // No need to validate again
};

// Internal function with type guarantees
function processWithGuaranteedTypes(validatedInput: ValidatedType) {
  // Safe to use without additional validation
}
```

## Employing advanced types instead of runtime checks

```typescript

// bad
someMethod: (items: Item[]) {
    if (items.length === 0) {
      return
    }
}

// good
someMethod: (items: NonEmptyArray<Item>) {
    // ...
}
```

## Use classes to encapsulate complex stateful logic, use functional programming for lower level data pipelines:

```typescript
class ResumeAnalyzer {
  method() {
    const items = pipe(operationOne, operationTwo, operationThree);
  }

  otherMethod() {}
}
```
