### How to navigate the ai-assets folder

- check `./repository.md` for general guideliness of the current workspace.
- you must follow `./coding-style.md` regardless of the task.
- when told to use "feature planning" - you MUST read and follow the `experts/planner.md` file.
- when told to do "diagnostics" - you MUST read and follow the `experts/diagnostics.md` file.
- when told to do "testing" - you MUST read and follow the `experts/testing.md` file.
- when your task involves front-end work - you must read and follow the `experts/front-end.md` file.
- when your task involves back-end work - you must read and follow the `experts/back-end.md` file.

### General shared instructions

- DO NOT write new test cases unless the prompt explicitly tells you to
- DO NOT export anything if its not yet used anywhere, e.g. exporting each new class you created in a library
- restrain from adding too many code comments, concentrate on situations where we really need it and explain "why we're doing" a certaion decision or logic.
- use the "context7" MCP for tasks that require a lot of context and background information about libraries.
- use the "memory" MCP proactively to store or retrieve memories which might be useful either for the current task or for future ones.
- proactively try to use your own built-in "todo list" / "task list" when planning work. (if you have such)

-after completing tasks or doing analysis - you could be proactive and propose updates or general improvements to the files in the root `ai-assets` folder if you find opportunity.

- you can find information about the products we're building through this monorepo inside the `ai-assets/products` folder.

- if the task you finished was repetitive and might be needed again, you should suggest: "Would you like me to add this task to the common-workflows.md" file if it's not already there.
