/**
 * Shared utilities for error handling and async operations
 */

/**
 * Checks if an error is related to AI unavailability
 */
export function isAIUnavailableError(error: unknown): boolean {
  return (
    error instanceof Error &&
    (error.message.includes('AI unavailable') ||
      error.message.includes('model unavailable') ||
      error.message.includes('service unavailable'))
  );
}

/**
 * Checks if an error is related to timeout
 */
export function isTimeoutError(error: unknown): boolean {
  return (
    error instanceof Error &&
    (error.message.includes('timeout') ||
      error.message.includes('timed out') ||
      error.message.includes('TIMEOUT'))
  );
}

/**
 * Wraps a promise with a timeout
 */
export async function withTimeout<T>(
  promise: Promise<T>,
  timeoutMs: number,
  operationName: string
): Promise<T> {
  return Promise.race([
    promise,
    new Promise<T>((_, reject) =>
      setTimeout(() => {
        reject(new Error(`${operationName} timed out after ${timeoutMs}ms`));
      }, timeoutMs)
    ),
  ]);
}
