import { Job } from '../entities/job';

/**
 * Helper function to determine confidence level from numeric score
 */
export const getConfidenceLevel = (
  confidence: number
): 'low' | 'medium' | 'high' => {
  if (confidence >= 0.7) return 'high';
  if (confidence >= 0.4) return 'medium';
  return 'low';
};

export const formatJobContext = (
  job: Pick<Job, 'title' | 'description' | 'skills' | 'qualifications'>
) => ({
  jobTitle: job.title || 'Not specified',
  jobDescription: job.description || 'Not specified',
  jobSkills: formatJobSkills(job),
  jobQualifications: formatJobQualifications(job),
});

const formatJobSkills = (job: Pick<Job, 'skills'>): string => {
  if (!job.skills || job.skills.length === 0) {
    return 'Not specified';
  }

  return job.skills
    .map((skill) => {
      const skillName = skill.name || skill;
      const keywords = skill.keywords?.length
        ? ` (${skill.keywords.join(', ')})`
        : '';
      const level = skill.level ? ` [${skill.level}]` : '';
      return `- ${skillName}${keywords}${level}`;
    })
    .join('\n');
};

const formatJobQualifications = (job: Pick<Job, 'qualifications'>): string => {
  if (!job.qualifications || job.qualifications.length === 0) {
    return 'Not specified';
  }

  return job.qualifications.map((qual) => `- ${qual}`).join('\n');
};
