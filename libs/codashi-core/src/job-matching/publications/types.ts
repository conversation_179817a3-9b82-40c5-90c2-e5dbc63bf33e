import { z } from 'zod';

import type { ResumeEntity } from '../../entities/resume';

/**
 * Represents a publication entry from a single resume for analysis
 */
export type PublicationItem = ResumeEntity<'publication'> & {
  /** artificially generated stable id */
  id: string;
  /** normalized title alias (mapped from name) */
  title?: string | null;
  /** venue or publisher */
  venue?: string | null;
  /** optional enrichment fields */
  authors?: string[] | null;
  publicationType?:
    | 'journal'
    | 'conference'
    | 'workshop'
    | 'preprint'
    | 'book'
    | 'chapter'
    | 'thesis'
    | 'whitepaper'
    | 'blog'
    | 'presentation'
    | 'other'
    | null;
  doi?: string | null;
  topics?: string[] | null;
  impactMetrics?: {
    citations?: number | null;
    hIndexSignal?: number | null;
  } | null;
};

/**
 * Represents a publication relevance score
 */
export type PublicationScore = z.infer<
  typeof publicationScoreSchema
>['scores'][number];

/**
 * Configuration options for single-resume publications analysis
 */
export type PublicationAnalysisOptions = {
  /** Timeout in milliseconds for AI operations */
  timeoutMs?: number;
  /** Batch size for LLM calls (number of publications per call) */
  batchSize?: number;
};

/**
 * Complete analysis result for single resume publications matching
 */
export type PublicationAnalysis = {
  /** All publications extracted from the resume */
  publications: PublicationItem[];
  /** Publications with relevance scores */
  scoredPublications: PublicationScore[];
  /** Summary statistics */
  summary: {
    totalPublications: number;
    averageRelevanceScore: number;
    highRelevanceCount: number;
  };
};

/**
 * Schema for AI-powered single resume publication scoring results
 */
export const publicationScoreSchema = z.object({
  scores: z.array(
    z.object({
      publicationId: z.string(),
      relevanceScore: z.number().min(1).max(5),
      reasoning: z.string().max(300),
    })
  ),
});

export type PublicationScoreResult = z.infer<typeof publicationScoreSchema>;
