import type { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { StructuredOutputParser } from '@langchain/core/output_parsers';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import { RunnableSequence } from '@langchain/core/runnables';

import { isNonEmptyArray, NonEmptyArray } from '@awe/core';
import type { Job } from '../../entities/job';
import { withTimeout } from '../../utils/common-utils';
import { formatJobContext } from '../utils';
import {
  formatPublicationForPrompt,
  PUBLICATION_SCORING_STANDALONE_PROMPT,
} from './prompt-components';
import type {
  PublicationAnalysisOptions,
  PublicationItem,
  PublicationScore,
} from './types';
import { publicationScoreSchema, type PublicationScoreResult } from './types';

/**
 * Scores publications from a single resume against job requirements.
 */
export class PublicationScorer {
  private readonly DEFAULT_BATCH_SIZE = 8;
  private readonly MAX_SAFE_TOKENS = 8000;

  constructor(private model: BaseChatModel) {}

  async scorePublications(
    publications: NonEmptyArray<PublicationItem>,
    job: Job,
    options: PublicationAnalysisOptions = {}
  ): Promise<PublicationScore[]> {
    const batchSize = options.batchSize || this.DEFAULT_BATCH_SIZE;
    const timeoutMs = options.timeoutMs || 30000;

    const totalTokens = this.estimateTotalTokens(publications, job);
    if (
      totalTokens <= this.MAX_SAFE_TOKENS &&
      publications.length <= batchSize
    ) {
      return await this.scoreBatch(publications, job, timeoutMs);
    } else {
      return await this.scoreInBatches(publications, job, batchSize, timeoutMs);
    }
  }

  private async scoreBatch(
    publications: NonEmptyArray<PublicationItem>,
    job: Job,
    timeoutMs: number
  ): Promise<PublicationScore[]> {
    const prompt = ChatPromptTemplate.fromTemplate(
      PUBLICATION_SCORING_STANDALONE_PROMPT
    );
    const parser = StructuredOutputParser.fromZodSchema(publicationScoreSchema);
    const chain = RunnableSequence.from([prompt, this.model, parser]);

    const publicationList = publications
      .map((p, index) => formatPublicationForPrompt(p, index))
      .join('\n\n');

    const jobContext = formatJobContext(job);

    const result = await withTimeout(
      chain.invoke({
        ...jobContext,
        publicationList,
        format_instructions: parser.getFormatInstructions(),
      }),
      timeoutMs,
      'Single resume publications scoring'
    );

    return this.mapResultsToScores(result, publications);
  }

  private async scoreInBatches(
    publications: PublicationItem[],
    job: Job,
    batchSize: number,
    timeoutMs: number
  ): Promise<PublicationScore[]> {
    const allScores: PublicationScore[] = [];
    for (let i = 0; i < publications.length; i += batchSize) {
      const batch = publications.slice(i, i + batchSize);

      if (!isNonEmptyArray(batch)) {
        return allScores;
      }

      const batchScores = await this.scoreBatch(
        batch as NonEmptyArray<PublicationItem>,
        job,
        timeoutMs
      );
      allScores.push(...batchScores);
    }
    return allScores;
  }

  private mapResultsToScores(
    result: PublicationScoreResult,
    publications: PublicationItem[]
  ): PublicationScore[] {
    const scores: PublicationScore[] = [];

    for (const scoreResult of result.scores) {
      const publication = publications.find(
        (p) => p.id === scoreResult.publicationId
      );

      if (publication) {
        scores.push({
          publicationId: scoreResult.publicationId,
          relevanceScore: scoreResult.relevanceScore,
          reasoning: scoreResult.reasoning,
        });
      }
    }

    return scores;
  }

  private estimateTotalTokens(
    publications: PublicationItem[],
    job: Job
  ): number {
    let tokenCount = 0;

    tokenCount += (job.title?.length || 0) / 4;
    tokenCount += (job.description?.length || 0) / 4;

    if (job.skills) {
      tokenCount += job.skills.reduce(
        (sum, skill) => sum + (skill.name?.length || 0) / 4,
        0
      );
    }

    for (const pub of publications) {
      tokenCount += (pub.name?.length || 0) / 4;
      tokenCount += (pub.summary?.length || 0) / 4;
      tokenCount += (pub.publisher?.length || 0) / 4;
      tokenCount += (pub.url?.length || 0) / 4;
    }

    return Math.ceil(tokenCount);
  }
}
