import { type ResumeEntity } from '../../entities/resume';

const JOB_CONTEXT_SECTION = `
Job Title: {jobTitle}
Job Description: {jobDescription}
Required Skills: {jobSkills}
Required Qualifications: {jobQualifications}
`;

/**
 * Publications context section for prompt injection
 */
const PUBLICATIONS_CONTEXT_SECTION = `
Publications to Analyze:
{publicationList}
`;

/**
 * Scoring criteria focused on publications relevance
 */
const PUBLICATIONS_SCORING_CRITERIA = `
Consider these factors in your scoring:
- Relevance of the publication's topic to the job description and required skills
- Publication type and venue (peer-reviewed journals/conferences typically carry more weight than blogs)
- Recency and continuing relevance (more recent publications may carry more weight)
- Authorship role (first/corresponding author signals stronger contribution)
- Evidence of impact (citations, adoption, downloads) when available
- Practical applicability to the role (applied research, tooling, case studies)

Scoring Scale:
5 = Highly relevant - Directly aligns with role, skills, and shows strong impact or senior authorship
4 = Very relevant - Strong alignment with some differences or moderate impact
3 = Moderately relevant - Some alignment or transferable value
2 = Somewhat relevant - Limited alignment
1 = Minimally relevant - Little to no connection to target role
`;

/**
 * Standalone prompt used for scoring publications
 */
export const PUBLICATION_SCORING_STANDALONE_PROMPT = `
You are an expert career advisor analyzing how relevant a candidate's publications are for a target job.

${JOB_CONTEXT_SECTION}

${PUBLICATIONS_CONTEXT_SECTION}

${PUBLICATIONS_SCORING_CRITERIA}

Please score each publication on a scale of 1-5 and provide concise reasoning for each score.

{format_instructions}
`;

/**
 * Format a publication entry for inclusion in prompts
 */
export const formatPublicationForPrompt = (
  publication: ResumeEntity<'publication'> & { id?: string },
  index?: number
): string => {
  const prefix =
    index !== undefined ? `Publication ${index + 1}` : 'Publication';
  const id = publication.id ? ` (ID: ${publication.id})` : '';

  let formatted = `${prefix}${id}:
Title: ${publication.name}
Publisher/Venue: ${publication.publisher || 'Not specified'}
Date: ${publication.release_date}
`;

  if (publication.url) {
    formatted += `URL: ${publication.url}\n`;
  }

  if (publication.summary) {
    formatted += `Summary: ${publication.summary}\n`;
  }

  return formatted.trim();
};
