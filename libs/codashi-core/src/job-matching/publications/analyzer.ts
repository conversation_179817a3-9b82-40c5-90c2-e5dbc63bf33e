import type { BaseChatModel } from '@langchain/core/language_models/chat_models';

import { isNonEmptyArray } from '@awe/core';
import type { Job } from '../../entities/job';
import type { Resume } from '../../entities/resume';
import {
  isAIUnavailableError,
  isTimeoutError,
  withTimeout,
} from '../../utils/common-utils';
import { PublicationExtractor } from './extractor';
import { PublicationScorer } from './scorer';
import type { PublicationAnalysis, PublicationAnalysisOptions } from './types';

/**
 * Class for analyzing publications matches between a single resume and a job posting.
 */
export class PublicationsAnalyzer {
  private readonly model: BaseChatModel;

  constructor(model: BaseChatModel) {
    this.model = model;
  }

  async analyzePublications(
    resume: Resume,
    job: Job,
    options: PublicationAnalysisOptions = {}
  ): Promise<PublicationAnalysis> {
    const finalOptions = {
      timeoutMs: 30000,
      batchSize: 5,
      ...options,
    } satisfies PublicationAnalysisOptions;

    try {
      // Step 1: Extract publications from the resume
      const extractor = new PublicationExtractor();
      const publications = extractor.extractPublications(resume);

      if (!isNonEmptyArray(publications)) {
        return {
          publications: [],
          scoredPublications: [],
          summary: {
            totalPublications: 0,
            averageRelevanceScore: 0,
            highRelevanceCount: 0,
          },
        };
      }

      // Step 2: Score publications against job requirements
      const scorer = new PublicationScorer(this.model);
      const scoredPublications = await withTimeout(
        scorer.scorePublications(publications, job, finalOptions),
        finalOptions.timeoutMs,
        'Single resume publications scoring'
      );

      const summary = this.calculateSummary(scoredPublications);

      return {
        publications,
        scoredPublications,
        summary,
      };
    } catch (error) {
      if (isAIUnavailableError(error)) {
        throw new PublicationMatchError(
          'AI model is currently unavailable for publications analysis',
          'AI_UNAVAILABLE'
        );
      }

      if (isTimeoutError(error)) {
        throw new PublicationMatchError(
          'Publications analysis timed out',
          'TIMEOUT'
        );
      }

      throw new PublicationMatchError(
        `Publications analysis failed: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        'INVALID_INPUT'
      );
    }
  }

  private calculateSummary(
    scoredPublications: PublicationAnalysis['scoredPublications']
  ): PublicationAnalysis['summary'] {
    const totalPublications = scoredPublications.length;

    if (totalPublications === 0) {
      return {
        totalPublications: 0,
        averageRelevanceScore: 0,
        highRelevanceCount: 0,
      };
    }

    const totalScore = scoredPublications.reduce(
      (sum, sp) => sum + sp.relevanceScore,
      0
    );
    const averageRelevanceScore =
      Math.round((totalScore / totalPublications) * 10) / 10;
    const highRelevanceCount = scoredPublications.filter(
      (sp) => sp.relevanceScore >= 4
    ).length;

    return {
      totalPublications,
      averageRelevanceScore,
      highRelevanceCount,
    };
  }
}

class PublicationMatchError extends Error {
  constructor(
    message: string,
    public readonly code:
      | 'AI_UNAVAILABLE'
      | 'TIMEOUT'
      | 'INVALID_INPUT'
      | 'NO_PUBLICATIONS'
  ) {
    super(message);
    this.name = 'PublicationMatchError';
  }
}
