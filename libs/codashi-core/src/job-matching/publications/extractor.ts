import { type Resume } from '../../entities/resume';
import type { PublicationItem } from './types';

/**
 * Extracts publication entries from a single resume without consolidation.
 */
export class PublicationExtractor {
  extractPublications(resume: Resume): PublicationItem[] {
    const publications: PublicationItem[] = [];

    const publicationsSection = resume.sections.find(
      (section) => section.name === 'publications'
    );

    if (!publicationsSection || !publicationsSection.items) {
      return publications;
    }

    for (const pubItem of publicationsSection.items) {
      const publication: PublicationItem = {
        id: this.generatePublicationId(
          pubItem.name,
          pubItem.publisher,
          pubItem.release_date
        ),
        name: pubItem.name,
        publisher: pubItem.publisher,
        release_date: pubItem.release_date,
        url: pubItem.url,
        summary: pubItem.summary,
        // enrichment fields left empty for now
        title: pubItem.name ?? null,
        venue: pubItem.publisher ?? null,
        authors: null,
        publicationType: null,
        doi: null,
        topics: null,
        impactMetrics: null,
      } as PublicationItem;

      publications.push(publication);
    }

    // Sort by release_date, most recent first
    return publications.sort((a, b) => {
      if (a.release_date && b.release_date) {
        return (
          new Date(b.release_date).getTime() -
          new Date(a.release_date).getTime()
        );
      }
      return 0;
    });
  }

  private generatePublicationId(
    title: string | undefined,
    venue: string | null,
    date: string | undefined
  ): string {
    const normalized = `${this.normalizeText(title)}-${this.normalizeText(
      venue
    )}-${date || 'unknown'}`;
    return normalized.replace(/[^\w-]/g, '').substring(0, 60);
  }

  private normalizeText(text: string | null | undefined): string {
    if (!text) return '';
    return text
      .toLowerCase()
      .trim()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-');
  }

  /**
   * Rough token estimate for a single publication entry
   */
  estimatePublicationTokens(publication: PublicationItem): number {
    let tokenCount = 0;
    tokenCount += (publication.name?.length || 0) / 4;
    tokenCount += (publication.summary?.length || 0) / 4;
    tokenCount += (publication.publisher?.length || 0) / 4;
    tokenCount += (publication.url?.length || 0) / 4;
    return Math.ceil(tokenCount);
  }

  estimateTotalTokens(publications: PublicationItem[]): number {
    return publications.reduce(
      (sum, p) => sum + this.estimatePublicationTokens(p),
      0
    );
  }
}
