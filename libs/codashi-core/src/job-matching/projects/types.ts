import { z } from 'zod';

import type { ResumeEntity } from '../../entities/resume';

/**
 * Represents a resume project with its relevance score from single resume analysis
 */
export type ProjectItem = ResumeEntity<'project'> & {
  // artificially generated
  id: string;
};

/**
 * Represents a project relevance score
 */
export type ProjectScore = {
  /** The project being scored */
  project: ProjectItem;
  /** Relevance score from 1-5 (5 being most relevant) */
  relevanceScore: number;
  /** AI reasoning for the assigned score */
  reasoning: string;
};

/**
 * Configuration options for single resume projects analysis
 */
export type ProjectAnalysisOptions = {
  /** Timeout in milliseconds for AI operations */
  timeoutMs?: number;
  /** Batch size for LLM calls (number of projects per call) */
  batchSize?: number;
};

/**
 * Complete analysis result for single resume projects matching
 */
export type ProjectAnalysis = {
  /** All projects from the single resume */
  projects: ProjectItem[];
  /** Projects with relevance scores */
  scoredProjects: ProjectScore[];
  /** Summary statistics */
  summary: {
    /** Total number of projects */
    totalProjects: number;
    /** Average relevance score across all projects */
    averageRelevanceScore: number;
    /** Number of highly relevant projects (score >= 4) */
    highRelevanceCount: number;
  };
};

/**
 * Schema for AI-powered single resume project scoring results
 */
export const projectScoreSchema = z.object({
  scores: z.array(
    z.object({
      projectId: z.string(),
      relevanceScore: z.number().min(1).max(5),
      reasoning: z.string().max(300),
    })
  ),
});

export type ProjectScoreResult = z.infer<typeof projectScoreSchema>;
