import type { Resume } from '../../entities/resume';
import type { ProjectItem } from './types';

/**
 * Extracts projects from a single resume without consolidation.
 */
export class ProjectExtractor {
  extractProjects(resume: Resume): ProjectItem[] {
    const projects: ProjectItem[] = [];

    const projectsSection = resume.sections.find((s) => s.name === 'projects');
    if (!projectsSection || !projectsSection.items) return projects;

    for (const prj of projectsSection.items) {
      const project: ProjectItem = {
        id: this.generateProjectId(prj.name, prj.type, prj.start_date),
        name: prj.name,
        description: prj.description,
        highlights: prj.highlights || null,
        keywords: prj.keywords || null,
        start_date: prj.start_date,
        end_date: prj.end_date || null,
        url: prj.url || null,
        roles: prj.roles || null,
        entity: prj.entity || null,
        type: prj.type,
      };
      projects.push(project);
    }

    // Sort by start date, most recent first
    return projects.sort(
      (a, b) =>
        new Date(b.start_date).getTime() - new Date(a.start_date).getTime()
    );
  }

  private generateProjectId(
    name: string | undefined,
    type: string | undefined,
    startDate: string | undefined
  ): string {
    const normalized = `${this.normalize(name)}-${this.normalize(type)}-${
      startDate || 'unknown'
    }`;
    return normalized.replace(/[^\w-]/g, '').substring(0, 50);
  }

  private normalize(v?: string): string {
    if (!v) return '';
    return v
      .toLowerCase()
      .trim()
      .replace(/[^\w\s]/g, '')
      .replace(/\s+/g, ' ');
  }
}
