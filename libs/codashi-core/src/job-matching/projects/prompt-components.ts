/**
 * Modular prompt components for projects analysis
 */

import type { ResumeEntity } from '../../entities/resume';

/**
 * Projects context section - provides project information
 */
const PROJECTS_CONTEXT_SECTION = `
Projects to Analyze:
{projectList}
`;

/**
 * Scoring criteria for projects relevance
 */
const PROJECTS_SCORING_CRITERIA = `
Consider these factors in your scoring:
- Technologies and tools used vs job stack
- Scope and complexity (team size, duration, responsibilities)
- Achievements and measurable impact
- Relevance of domain and problem space to the job
- Recency and progression (more recent projects may carry more weight)

Scoring Scale:
5 = Highly relevant - Directly aligns with role, tech stack, and impact
4 = Very relevant - Strong alignment with some differences
3 = Moderately relevant - Partial alignment or transferable value
2 = Somewhat relevant - Limited alignment
1 = Minimally relevant - Little to no connection to target role
`;

/**
 * Full standalone prompt for projects scoring
 */
export const PROJECT_SCORING_STANDALONE_PROMPT = `
You are an expert career advisor analyzing how relevant a candidate's projects are for a target job.

Job Context:
Job Title: {jobTitle}
Job Description: {jobDescription}
Required Skills: {jobSkills}
Required Qualifications: {jobQualifications}

${PROJECTS_CONTEXT_SECTION}

${PROJECTS_SCORING_CRITERIA}

Please score each project on a scale of 1-5 and provide concise reasoning.

{format_instructions}
`;

/**
 * Helper function to format a project for prompts
 */
export const formatProjectForPrompt = (
  project: ResumeEntity<'project'> & { id?: string },
  index?: number
): string => {
  const prefix = index !== undefined ? `Project ${index + 1}` : 'Project';
  const id = project.id ? ` (ID: ${project.id})` : '';

  let formatted = `${prefix}${id}:
Name: ${project.name}
Type: ${project.type}
Duration: ${project.start_date} to ${project.end_date || 'Present'}`;

  if (project.entity) {
    formatted += `\nEntity: ${project.entity}`;
  }
  if (project.roles && project.roles.length > 0) {
    formatted += `\nRoles: ${project.roles.join(', ')}`;
  }
  if (project.keywords && project.keywords.length > 0) {
    formatted += `\nTechnologies: ${project.keywords.join(', ')}`;
  }
  if (project.description) {
    formatted += `\nDescription: ${project.description}`;
  }
  if (project.highlights && project.highlights.length > 0) {
    formatted += `\nHighlights:\n${project.highlights
      .map((h) => `• ${h}`)
      .join('\n')}`;
  }

  return formatted;
};
