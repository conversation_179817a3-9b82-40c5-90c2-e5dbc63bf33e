import { type BaseChatModel } from '@langchain/core/language_models/chat_models';

import { isNonEmptyArray } from '@awe/core';
import { type Job } from '../../entities/job';
import { type Resume } from '../../entities/resume';
import {
  isAIUnavailableError,
  isTimeoutError,
  withTimeout,
} from '../../utils/common-utils';
import { EducationExtractor } from './extractor';
import { EducationScorer } from './scorer';
import { type EducationAnalysis, type EducationAnalysisOptions } from './types';

export class EducationAnalyzer {
  private readonly model: BaseChatModel;

  constructor(model: BaseChatModel) {
    this.model = model;
  }

  async analyzeEducation(
    resume: Resume,
    job: Job,
    options: EducationAnalysisOptions = {}
  ): Promise<EducationAnalysis> {
    const finalOptions = {
      timeoutMs: 30000,
      batchSize: 5,
      ...options,
    } satisfies EducationAnalysisOptions;

    try {
      const extractor = new EducationExtractor();
      const educations = extractor.extractEducations(resume);

      if (!isNonEmptyArray(educations)) {
        return {
          educations: [],
          scoredEducations: [],
          summary: {
            totalEducations: 0,
            averageRelevanceScore: 0,
            highRelevanceCount: 0,
          },
        };
      }

      const scorer = new EducationScorer(this.model);
      const scoredEducations = await withTimeout(
        scorer.scoreEducations(educations, job, finalOptions),
        finalOptions.timeoutMs,
        'Single resume education scoring'
      );

      const summary = this.calculateSummary(scoredEducations);

      return {
        educations,
        scoredEducations,
        summary,
      };
    } catch (error) {
      if (isAIUnavailableError(error)) {
        throw new EducationMatchError(
          'AI model is currently unavailable for education analysis',
          'AI_UNAVAILABLE'
        );
      }

      if (isTimeoutError(error)) {
        throw new EducationMatchError(
          'Education analysis timed out',
          'TIMEOUT'
        );
      }

      throw new EducationMatchError(
        `Education analysis failed: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        'INVALID_INPUT'
      );
    }
  }

  private calculateSummary(
    scoredEducations: EducationAnalysis['scoredEducations']
  ): EducationAnalysis['summary'] {
    const totalEducations = scoredEducations.length;

    if (totalEducations === 0) {
      return {
        totalEducations: 0,
        averageRelevanceScore: 0,
        highRelevanceCount: 0,
      };
    }

    const totalScore = scoredEducations.reduce(
      (sum, scored) => sum + scored.relevanceScore,
      0
    );
    const averageRelevanceScore =
      Math.round((totalScore / totalEducations) * 10) / 10;

    const highRelevanceCount = scoredEducations.filter(
      (scored) => scored.relevanceScore >= 4
    ).length;

    return {
      totalEducations,
      averageRelevanceScore,
      highRelevanceCount,
    };
  }
}

class EducationMatchError extends Error {
  constructor(
    message: string,
    public readonly code:
      | 'AI_UNAVAILABLE'
      | 'TIMEOUT'
      | 'INVALID_INPUT'
      | 'NO_EDUCATIONS'
  ) {
    super(message);
    this.name = 'EducationMatchError';
  }
}
