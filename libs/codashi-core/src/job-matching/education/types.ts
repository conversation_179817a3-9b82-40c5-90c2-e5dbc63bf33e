import { z } from 'zod';

import { type ResumeEntity } from '../../entities/resume';

/**
 * Represents an education entry with its relevance score from single resume analysis
 */
export type EducationScore = {
  /** The education entry being scored */
  education: EducationEntry;
  /** Relevance score from 1-5 (5 being most relevant) */
  relevanceScore: number;
  /** AI reasoning for the assigned score */
  reasoning: string;
};

/**
 * Configuration options for single resume education analysis
 */
export type EducationAnalysisOptions = {
  /** Timeout in milliseconds for AI operations */
  timeoutMs?: number;
  /** Batch size for LLM calls (number of education entries per call) */
  batchSize?: number;
};

/**
 * Complete analysis result for single resume education matching
 */
export type EducationAnalysis = {
  /** All education entries from the single resume */
  educations: EducationEntry[];
  /** Education entries with relevance scores */
  scoredEducations: EducationScore[];
  /** Summary statistics */
  summary: {
    /** Total number of education entries */
    totalEducations: number;
    /** Average relevance score across all education entries */
    averageRelevanceScore: number;
    /** Number of highly relevant education entries (score >= 4) */
    highRelevanceCount: number;
  };
};

/**
 * Schema for AI-powered single resume education scoring results
 */
export const educationScoreSchema = z.object({
  scores: z.array(
    z.object({
      educationId: z.string(),
      relevanceScore: z.number().min(1).max(5),
      reasoning: z.string().max(300),
    })
  ),
});

export type EducationScoreResult = z.infer<typeof educationScoreSchema>;

/**
 * Represents an education entry from a single resume (no consolidation)
 */
export type EducationEntry = ResumeEntity<'education'> & {
  /** Unique identifier generated as a UUID string for each education entry */
  id: string;
};
