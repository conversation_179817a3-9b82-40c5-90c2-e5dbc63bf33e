import { type BaseChatModel } from '@langchain/core/language_models/chat_models';
import { StructuredOutputParser } from '@langchain/core/output_parsers';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import { RunnableSequence } from '@langchain/core/runnables';

import { NonEmptyArray } from '@awe/core';
import { type Job } from '../../entities/job';
import { withTimeout } from '../../utils/common-utils';
import { formatJobContext } from '../utils';
import {
  EDUCATION_SCORING_STANDALONE_PROMPT,
  formatEducationForPrompt,
} from './prompt-components';
import {
  type EducationAnalysisOptions,
  type EducationEntry,
  type EducationScore,
  type EducationScoreResult,
  educationScoreSchema,
} from './types';

export class EducationScorer {
  private readonly DEFAULT_BATCH_SIZE = 8;
  private readonly MAX_SAFE_TOKENS = 8000;

  constructor(private model: BaseChatModel) {}

  async scoreEducations(
    educations: NonEmptyArray<EducationEntry>,
    job: Job,
    options: EducationAnalysisOptions = {}
  ): Promise<EducationScore[]> {
    const batchSize = options.batchSize || this.DEFAULT_BATCH_SIZE;
    const timeoutMs = options.timeoutMs || 30000;

    const totalTokens = this.estimateTotalTokens(educations, job);

    if (totalTokens <= this.MAX_SAFE_TOKENS && educations.length <= batchSize) {
      return await this.scoreBatch(educations, job, timeoutMs);
    } else {
      return await this.scoreInBatches(educations, job, batchSize, timeoutMs);
    }
  }

  private async scoreBatch(
    educations: EducationEntry[],
    job: Job,
    timeoutMs: number
  ): Promise<EducationScore[]> {
    const prompt = ChatPromptTemplate.fromTemplate(
      EDUCATION_SCORING_STANDALONE_PROMPT
    );
    const parser = StructuredOutputParser.fromZodSchema(educationScoreSchema);
    const chain = RunnableSequence.from([prompt, this.model, parser]);

    const educationList = educations
      .map((edu, index) => formatEducationForPrompt(edu, index))
      .join('\n\n');

    const jobContext = formatJobContext(job);

    const result = await withTimeout(
      chain.invoke({
        ...jobContext,
        educationList,
        format_instructions: parser.getFormatInstructions(),
      }),
      timeoutMs,
      'Single resume education scoring'
    );

    return this.mapResultsToScores(result, educations);
  }

  private async scoreInBatches(
    educations: EducationEntry[],
    job: Job,
    batchSize: number,
    timeoutMs: number
  ): Promise<EducationScore[]> {
    const allScores: EducationScore[] = [];
    const failedBatches: EducationEntry[][] = [];

    for (let i = 0; i < educations.length; i += batchSize) {
      const batch = educations.slice(i, i + batchSize);
      try {
        const batchScores = await this.scoreBatch(batch, job, timeoutMs);
        allScores.push(...batchScores);
      } catch (error) {
        console.error(`Failed to score batch starting at index ${i}:`, error);
        failedBatches.push(batch);
      }
    }

    if (failedBatches.length > 0) {
      console.warn(`Failed to score ${failedBatches.length} batches`);
    }

    return allScores;
  }

  private mapResultsToScores(
    result: EducationScoreResult,
    educations: EducationEntry[]
  ): EducationScore[] {
    const scores: EducationScore[] = [];

    for (const scoreResult of result.scores) {
      const education = educations.find(
        (edu) => edu.id === scoreResult.educationId
      );

      if (education) {
        scores.push({
          education,
          relevanceScore: scoreResult.relevanceScore,
          reasoning: scoreResult.reasoning,
        });
      }
    }

    return scores;
  }

  private estimateTotalTokens(educations: EducationEntry[], job: Job): number {
    let tokenCount = 0;

    tokenCount += (job.title?.length || 0) / 4;
    tokenCount += (job.description?.length || 0) / 4;

    if (job.skills) {
      tokenCount += job.skills.reduce(
        (sum, skill) => sum + (skill.name?.length || 0) / 4,
        0
      );
    }

    for (const edu of educations) {
      tokenCount += (edu.institution?.length || 0) / 4;
      tokenCount += (edu.study_type?.length || 0) / 4;
      tokenCount += (edu.start_date?.length || 0) / 4;
      tokenCount += (edu.end_date?.length || 0) / 4;
      tokenCount += (edu.area?.length || 0) / 4;
    }

    return Math.ceil(tokenCount);
  }
}
