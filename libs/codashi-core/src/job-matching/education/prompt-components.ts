import { type ResumeEntity } from '../../entities/resume';

const JOB_CONTEXT_SECTION = `
Job Title: {jobTitle}
Job Description: {jobDescription}
Required Skills: {jobSkills}
Required Qualifications: {jobQualifications}
`;

const EDUCATION_CONTEXT_SECTION = `
Education to Analyze:
{educationList}
`;

const SCORING_CRITERIA_SECTION = `
Consider these factors in your scoring:
- Relevance of the field of study to the job requirements
- Reputation and quality of the educational institution
- Academic performance and honors
- Specific coursework or projects that align with the job description
- Extracurricular activities or research that demonstrate relevant skills

Scoring Scale:
5 = Highly relevant - Direct match with strong evidence of required knowledge and skills
4 = Very relevant - Good alignment with a strong academic background
3 = Moderately relevant - Some alignment, but may lack specific focus
2 = Somewhat relevant - Limited connection to the job requirements
1 = Minimally relevant - Little to no clear connection to the target role
`;

export const EDUCATION_SCORING_STANDALONE_PROMPT = `
You are an expert career advisor analyzing education relevance for job applications.

${JOB_CONTEXT_SECTION}

${EDUCATION_CONTEXT_SECTION}

${SCORING_CRITERIA_SECTION}

Please score each education entry on a scale of 1-5 based on its relevance to the target job.
Provide specific reasoning for each score, focusing on the alignment of the field of study, coursework, and other academic achievements with the job requirements.

{format_instructions}
`;

export const formatEducationForPrompt = (
  education: ResumeEntity<'education'> & { id?: string },
  index?: number
): string => {
  const prefix = index !== undefined ? `Education ${index + 1}` : 'Education';
  const id = education.id ? ` (ID: ${education.id})` : '';

  let formatted = `${prefix}${id}:
Institution: ${education.institution || 'Not specified'}
Degree: ${education.study_type || 'Not specified'}
Area: ${education.area || 'Not specified'}
Duration: ${education.start_date} to ${education.end_date || 'Present'}`;

  if (education.score) {
    formatted += `\nGPA: ${education.score}`;
  }

  if (education.courses && education.courses.length > 0) {
    formatted += `\nRelevant Courses:\n${education.courses
      .map((c: string) => `• ${c}`)
      .join('\n')}`;
  }

  return formatted;
};
