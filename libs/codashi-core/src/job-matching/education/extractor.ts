import { type Resume } from '../../entities/resume';
import { type EducationEntry } from './types';

/**
 * Extracts education entries from a single resume without any consolidation logic.
 *
 * This class replaces the complex consolidation logic with simple extraction,
 * processing exactly one resume to extract its education entries.
 */
export class EducationExtractor {
  /**
   * Extracts education entries from a single resume.
   *
   * @param resume - Single resume to extract education entries from
   * @returns Array of education entries from the resume
   */
  extractEducations(resume: Resume): EducationEntry[] {
    const educations: EducationEntry[] = [];

    // Find the education section in the resume
    const educationSection = resume.sections.find(
      (section) => section.name === 'education'
    );

    if (!educationSection || !educationSection.items) {
      return educations;
    }

    // Extract each education entry directly without consolidation
    for (const educationItem of educationSection.items) {
      const education: EducationEntry = {
        id: this.generateEducationId(
          educationItem.institution,
          educationItem.study_type ?? undefined,
          educationItem.start_date
        ),
        url: educationItem.url,
        institution: educationItem.institution,
        study_type: educationItem.study_type,
        area: educationItem.area,
        start_date: educationItem.start_date,
        end_date: educationItem.end_date,
        score: educationItem.score,
        courses: educationItem.courses,
      };

      educations.push(education);
    }

    // Sort by start date, most recent first
    return educations.sort((a, b) => {
      if (a.start_date && b.start_date) {
        return (
          new Date(b.start_date).getTime() - new Date(a.start_date).getTime()
        );
      }
      return 0;
    });
  }

  /**
   * Generates a unique ID for an education entry
   */
  private generateEducationId(
    institution: string | undefined,
    study_type: string | undefined,
    startDate: string | undefined
  ): string {
    const normalized = `${this.normalizeInstitutionName(
      institution
    )}-${this.normalizeStudyType(study_type)}-${startDate || 'unknown'}`;
    return normalized.replace(/[^\w-]/g, '').substring(0, 50);
  }

  /**
   * Normalizes institution names for ID generation
   */
  private normalizeInstitutionName(name: string | undefined): string {
    if (!name) return '';
    return name
      .toLowerCase()
      .trim()
      .replace(/\b(university|college|institute|school)\b\.?/g, '')
      .replace(/[^\w\s]/g, '')
      .replace(/\s+/g, ' ')
      .trim();
  }

  /**
   * Normalizes study types for ID generation
   */
  private normalizeStudyType(study_type: string | undefined): string {
    if (!study_type) return '';
    return study_type
      .toLowerCase()
      .trim()
      .replace(/[^\w\s]/g, '')
      .replace(/\s+/g, ' ')
      .trim();
  }

  /**
   * Estimates token count for an education entry (rough approximation)
   */
  estimateEducationTokens(education: EducationEntry): number {
    let tokenCount = 0;

    tokenCount += (education.institution?.length || 0) / 4;
    tokenCount += (education.study_type?.length || 0) / 4;
    tokenCount += (education.area?.length || 0) / 4;
    tokenCount += (education.courses?.join(' ')?.length || 0) / 4;

    return Math.ceil(tokenCount);
  }

  /**
   * Estimates total token count for multiple education entries
   */
  estimateTotalTokens(educations: EducationEntry[]): number {
    return educations.reduce(
      (total, edu) => total + this.estimateEducationTokens(edu),
      0
    );
  }
}
