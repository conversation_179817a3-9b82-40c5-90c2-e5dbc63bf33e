import { type BaseChatModel } from '@langchain/core/language_models/chat_models';
import { StructuredOutputParser } from '@langchain/core/output_parsers';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import { RunnableSequence } from '@langchain/core/runnables';

import { type Job } from '../../entities/job';
import { withTimeout } from '../../utils/common-utils';
import { formatJobContext } from '../utils';
import {
  AWARDS_SCORING_STANDALONE_PROMPT,
  formatAwardForPrompt,
} from './prompt-components';
import {
  type AwardAnalysisOptions,
  type AwardItem,
  type AwardScore,
  type AwardScoreResult,
  awardScoreSchema,
} from './types';

/**
 * Scores awards from a single resume against job requirements.
 */
export class AwardScorer {
  private readonly DEFAULT_BATCH_SIZE = 8;
  private readonly MAX_SAFE_TOKENS = 8000;

  constructor(private model: BaseChatModel) {}

  async scoreAwards(
    awards: AwardItem[],
    job: Job,
    options: AwardAnalysisOptions = {}
  ): Promise<AwardScore[]> {
    if (!awards.length) {
      return [];
    }

    const batchSize = options.batchSize || this.DEFAULT_BATCH_SIZE;
    const timeoutMs = options.timeoutMs || 30000;

    const totalTokens = this.estimateTotalTokens(awards, job);

    if (totalTokens <= this.MAX_SAFE_TOKENS && awards.length <= batchSize) {
      return await this.scoreBatch(awards, job, timeoutMs);
    } else {
      return await this.scoreInBatches(awards, job, batchSize, timeoutMs);
    }
  }

  private async scoreBatch(
    awards: AwardItem[],
    job: Job,
    timeoutMs: number
  ): Promise<AwardScore[]> {
    const prompt = ChatPromptTemplate.fromTemplate(
      AWARDS_SCORING_STANDALONE_PROMPT
    );
    const parser = StructuredOutputParser.fromZodSchema(awardScoreSchema);
    const chain = RunnableSequence.from([prompt, this.model, parser]);

    const awardsList = awards
      .map((award, index) => formatAwardForPrompt(award, index))
      .join('\n\n');

    const jobContext = formatJobContext(job);

    const result = await withTimeout(
      chain.invoke({
        ...jobContext,
        awardsList,
        format_instructions: parser.getFormatInstructions(),
      }),
      timeoutMs,
      'Single resume awards scoring'
    );

    return this.mapResultsToScores(result, awards);
  }

  private async scoreInBatches(
    awards: AwardItem[],
    job: Job,
    batchSize: number,
    timeoutMs: number
  ): Promise<AwardScore[]> {
    const allScores: AwardScore[] = [];
    const failedBatches: AwardItem[][] = [];

    for (let i = 0; i < awards.length; i += batchSize) {
      const batch = awards.slice(i, i + batchSize);
      try {
        const batchScores = await this.scoreBatch(batch, job, timeoutMs);
        allScores.push(...batchScores);
      } catch (error) {
        console.error(`Failed to score batch starting at index ${i}:`, error);
        failedBatches.push(batch);
      }
    }

    if (failedBatches.length > 0) {
      console.warn(`Failed to score ${failedBatches.length} batches`);
    }

    return allScores;
  }

  private mapResultsToScores(
    result: AwardScoreResult,
    awards: AwardItem[]
  ): AwardScore[] {
    const scores: AwardScore[] = [];

    for (const scoreResult of result.scores) {
      const award = awards.find((awd) => awd.id === scoreResult.awardId);

      if (award) {
        scores.push({
          awardId: scoreResult.awardId,
          relevanceScore: scoreResult.relevanceScore,
          reasoning: scoreResult.reasoning,
        });
      }
    }

    return scores;
  }

  private estimateTotalTokens(awards: AwardItem[], job: Job): number {
    let tokenCount = 0;

    tokenCount += (job.title?.length || 0) / 4;
    tokenCount += (job.description?.length || 0) / 4;

    if (job.skills) {
      tokenCount += job.skills.reduce(
        (sum, skill) => sum + (skill.name?.length || 0) / 4,
        0
      );
    }

    for (const award of awards) {
      tokenCount += (award.title?.length || 0) / 4;
      tokenCount += (award.awarder?.length || 0) / 4;
      tokenCount += (award.date?.length || 0) / 4;
      tokenCount += (award.summary?.length || 0) / 4;
    }

    return Math.ceil(tokenCount);
  }
}
