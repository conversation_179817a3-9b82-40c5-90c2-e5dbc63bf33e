import { isNonEmptyArray } from '@awe/core';
import type { Resume } from '../../entities/resume';
import type { AwardItem } from './types';

/**
 * Extracts awards from a single resume without consolidation.
 */
export class AwardExtractor {
  extractAwards(resume: Resume): AwardItem[] {
    const awardsSection = resume.sections?.find(
      (section) => section.name === 'awards'
    );

    if (!awardsSection || !isNonEmptyArray(awardsSection.items)) {
      return [];
    }

    const awards = awardsSection.items.map((award) => ({
      id: this.generateAwardId(award.title, award.awarder, award.date),
      title: award.title,
      awarder: award.awarder,
      date: award.date,
      summary: award.summary,
    }));

    // Sort by date, most recent first if dates exist
    return awards.sort((a, b) => {
      const da = new Date(a.date).getTime();
      const db = new Date(b.date).getTime();

      if (isNaN(da) && isNaN(db)) return 0;
      if (isNaN(da)) return 1;
      if (isNaN(db)) return -1;

      return db - da;
    });
  }

  private generateAwardId(
    title: string,
    awarder: string,
    date: string
  ): string {
    const normalized = `${this.normalize(title)}-${this.normalize(awarder)}-${
      date || 'unknown'
    }`;
    return normalized.replace(/[^\w-]/g, '').substring(0, 50);
  }

  private normalize(v?: string | null): string {
    if (!v) return '';
    return v
      .toLowerCase()
      .trim()
      .replace(/[^\w\s]/g, '')
      .replace(/\s+/g, ' ');
  }
}
