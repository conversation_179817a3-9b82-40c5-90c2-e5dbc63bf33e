import { z } from 'zod';

/**
 * Represents an award item extracted from a single resume
 */
export type AwardItem = {
  // artificially generated stable id
  id: string;
  title: string;
  awarder: string;
  date: string;
  summary: string;
};

/**
 * Configuration options for single resume awards analysis
 */
export type AwardAnalysisOptions = {
  /** Timeout in milliseconds for AI operations */
  timeoutMs?: number;
  /** Batch size for LLM calls (number of awards per call) */
  batchSize?: number;
};

/**
 * Complete analysis result for single resume awards matching
 */
export type AwardAnalysis = {
  /** All awards from the single resume */
  awards: AwardItem[];
  /** Awards with relevance scores */
  scoredAwards: AwardScore[];
  /** Summary statistics */
  summary: {
    /** Total number of awards */
    totalAwards: number;
    /** Average relevance score across all awards */
    averageRelevanceScore: number;
    /** Number of highly relevant awards (score >= 4) */
    highRelevanceCount: number;
  };
};

/**
 * Schema for AI-powered single resume awards scoring results
 */
export const awardScoreSchema = z.object({
  scores: z.array(
    z.object({
      awardId: z.string(),
      relevanceScore: z.number().min(1).max(5),
      reasoning: z.string().max(300),
    })
  ),
});

export type AwardScoreResult = z.infer<typeof awardScoreSchema>;

export type AwardScore = z.infer<typeof awardScoreSchema>['scores'][number];
