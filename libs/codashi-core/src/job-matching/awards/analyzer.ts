import type { BaseChatModel } from '@langchain/core/language_models/chat_models';

import { isNonEmptyArray } from '@awe/core';
import type { Job } from '../../entities/job';
import type { Resume } from '../../entities/resume';
import {
  isAIUnavailableError,
  isTimeoutError,
  withTimeout,
} from '../../utils/common-utils';
import { AwardExtractor } from './extractor';
import { AwardScorer } from './scorer';
import type { AwardAnalysis, AwardAnalysisOptions } from './types';

/**
 * Analyzes awards relevance for a single resume against a job posting.
 */
export class AwardAnalyzer {
  private readonly model: BaseChatModel;

  constructor(model: BaseChatModel) {
    this.model = model;
  }

  async analyzeAwards(
    resume: Resume,
    job: Job,
    options: AwardAnalysisOptions = {}
  ): Promise<AwardAnalysis> {
    const finalOptions = {
      timeoutMs: 30000,
      batchSize: 12,
      ...options,
    } satisfies AwardAnalysisOptions;

    try {
      const extractor = new AwardExtractor();
      const awards = extractor.extractAwards(resume);

      if (!isNonEmptyArray(awards)) {
        return {
          awards: [],
          scoredAwards: [],
          summary: {
            totalAwards: 0,
            averageRelevanceScore: 0,
            highRelevanceCount: 0,
          },
        };
      }

      const scorer = new AwardScorer(this.model);
      const scoredAwards = await withTimeout(
        scorer.scoreAwards(awards, job, finalOptions),
        finalOptions.timeoutMs,
        'Single resume awards scoring'
      );

      const summary = this.calculateSummary(scoredAwards);

      return {
        awards,
        scoredAwards,
        summary,
      };
    } catch (error) {
      if (isAIUnavailableError(error)) {
        throw new AwardMatchError(
          'AI model is currently unavailable for awards analysis',
          'AI_UNAVAILABLE'
        );
      }

      if (isTimeoutError(error)) {
        throw new AwardMatchError('Awards analysis timed out', 'TIMEOUT');
      }

      throw new AwardMatchError(
        `Awards analysis failed: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        'INVALID_INPUT'
      );
    }
  }

  private calculateSummary(
    scored: AwardAnalysis['scoredAwards']
  ): AwardAnalysis['summary'] {
    const total = scored.length;

    if (total === 0) {
      return {
        totalAwards: 0,
        averageRelevanceScore: 0,
        highRelevanceCount: 0,
      };
    }

    const totalScore = scored.reduce((sum, s) => sum + s.relevanceScore, 0);
    const averageRelevanceScore = Math.round((totalScore / total) * 10) / 10;
    const highRelevanceCount = scored.filter(
      (s) => s.relevanceScore >= 4
    ).length;

    return {
      totalAwards: total,
      averageRelevanceScore,
      highRelevanceCount,
    };
  }
}

class AwardMatchError extends Error {
  constructor(
    message: string,
    public readonly code: 'AI_UNAVAILABLE' | 'TIMEOUT' | 'INVALID_INPUT'
  ) {
    super(message);
    this.name = 'AwardMatchError';
  }
}
