import { type AwardItem } from './types';

const JOB_CONTEXT_SECTION = `
Job Title: {jobTitle}
Job Description: {jobDescription}
Required Skills: {jobSkills}
Required Qualifications: {jobQualifications}
`;

const AWARDS_CONTEXT_SECTION = `
Awards to Analyze:
{awardsList}
`;

const SCORING_CRITERIA_SECTION = `
Consider these factors in your scoring:
- Relevance of the award to the job requirements
- Recognition level (local, regional, national, international)
- Prestige of the awarding organization
- How recently the award was received
- Skills or qualities demonstrated by the award

Scoring Scale:
5 = Highly relevant - Direct match with job requirements, demonstrating key skills
4 = Very relevant - Strong alignment with required skills or achievements
3 = Moderately relevant - Some alignment, but may not directly address key requirements
2 = Somewhat relevant - Limited connection to the job requirements
1 = Minimally relevant - Little to no clear connection to the target role
`;

export const AWARDS_SCORING_STANDALONE_PROMPT = `
You are an expert career advisor analyzing awards and recognition relevance for job applications.

${JOB_CONTEXT_SECTION}

${AWARDS_CONTEXT_SECTION}

${SCORING_CRITERIA_SECTION}

Please score each award entry on a scale of 1-5 based on its relevance to the target job.
Provide specific reasoning for each score, focusing on the alignment of the award with the job requirements and the skills it demonstrates.

{format_instructions}
`;

export const formatAwardForPrompt = (
  award: AwardItem,
  index?: number
): string => {
  const prefix = index !== undefined ? `Award ${index + 1}` : 'Award';
  const id = award.id ? ` (ID: ${award.id})` : '';

  let formatted = `${prefix}${id}:
Title: ${award.title || 'Not specified'}
Awarder: ${award.awarder || 'Not specified'}
Date: ${award.date || 'Not specified'}`;

  if (award.summary) {
    formatted += `\nSummary: ${award.summary}`;
  }

  return formatted;
};
