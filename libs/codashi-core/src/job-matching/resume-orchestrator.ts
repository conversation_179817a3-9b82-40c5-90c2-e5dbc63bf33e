import type { BaseChatModel } from '@langchain/core/language_models/chat_models';

import type { Job } from '../entities/job';
import type { Resume } from '../entities/resume';
import { AwardAnalyzer } from './awards';
import { CertificationAnalyzer } from './certifications';
import { EducationAnalyzer } from './education';
import { ExperienceAnalyzer } from './experience/analyzer';
import { InterestAnalyzer } from './interests';
import { ProjectAnalyzer } from './projects';
import { PublicationsAnalyzer } from './publications/analyzer';
import { ScoringService } from './scoring/scoring-service';
import { SkillAnalyzer } from './skills/analyzer';
import { SummaryService } from './summary/summary-service';
import { TitleAnalyzer } from './title/analyzer';
import { type AnalysisOptions, type AnalysisResult } from './types';

/**
 * Orchestrates complete analysis of a single resume against a job posting.
 *
 * This class performs comprehensive analysis including:
 * - Experience matching and scoring
 * - Skills analysis (direct, transferable, missing)
 * - Title matching and optimization
 * - Projects relevance scoring
 * - Overall recommendation and summary
 */
export class ResumeOrchestrator {
  private readonly model: BaseChatModel;
  private readonly scoringService: ScoringService;
  private readonly summaryService: SummaryService;

  constructor(model: BaseChatModel) {
    this.model = model;
    this.scoringService = new ScoringService();
    this.summaryService = new SummaryService(this.scoringService);
  }

  /**
   * Analyzes a single resume against a job posting.
   *
   * @param resume - Single resume to analyze
   * @param job - Job posting to match against
   * @param options - Optional configuration for all analysis types
   * @returns Promise resolving to complete resume analysis result
   *
   * @example
   * ```typescript
   * const orchestrator = new ResumeOrchestrator(mistralModel);
   * const analysis = await orchestrator.analyzeResume(
   *   resume,
   *   jobPosting,
   *   {
   *     includeSourceDetails: true,
   *     experience: { includeImprovements: true },
   *     skills: { confidenceThreshold: 2 },
   *     title: { suggestImprovements: true }
   *   }
   * );
   *
   * console.log(`Overall score: ${analysis.overallSummary.totalScore}`);
   * console.log(`Match quality: ${analysis.overallSummary.matchQuality}`);
   * console.log(`Recommended: ${analysis.overallSummary.isRecommended}`);
   * ```
   */
  public async analyzeResume(
    resume: Resume,
    job: Job,
    options: AnalysisOptions = {}
  ): Promise<AnalysisResult> {
    try {
      const finalOptions = {
        includeSourceDetails: false,
        timeoutMs: 60000, // 1 minute total timeout
        ...options,
      };

      const [
        experienceAnalysis,
        skillsAnalysis,
        titleAnalysis,
        projectsAnalysis,
        educationAnalysis,
        certificationsAnalysis,
        awardsAnalysis,
        interestsAnalysis,
        publicationsAnalysis,
      ] = await Promise.all([
        new ExperienceAnalyzer(this.model).analyzeExperience(
          resume,
          job,
          finalOptions.experience
        ),
        new SkillAnalyzer(this.model).analyzeSkills(
          resume,
          job,
          finalOptions.skills
        ),
        new TitleAnalyzer(this.model).analyzeTitle(
          resume,
          job,
          finalOptions.title
        ),
        new ProjectAnalyzer(this.model).analyzeProjects(
          resume,
          job,
          finalOptions.projects
        ),
        new EducationAnalyzer(this.model).analyzeEducation(
          resume,
          job,
          finalOptions.education
        ),
        new CertificationAnalyzer(this.model).analyzeCertifications(
          resume,
          job,
          finalOptions.certifications
        ),
        new AwardAnalyzer(this.model).analyzeAwards(
          resume,
          job,
          finalOptions.awards
        ),
        new InterestAnalyzer(this.model).analyzeInterests(
          resume,
          job,
          finalOptions.interests
        ),
        new PublicationsAnalyzer(this.model).analyzePublications(
          resume,
          job,
          finalOptions.publications
        ),
      ]);

      // Calculate overall summary using the SummaryService
      const overallSummary = this.summaryService.calculateOverallSummary(
        experienceAnalysis,
        skillsAnalysis,
        titleAnalysis,
        projectsAnalysis,
        educationAnalysis,
        certificationsAnalysis,
        awardsAnalysis,
        interestsAnalysis,
        publicationsAnalysis
      );

      return {
        experience: experienceAnalysis,
        skills: skillsAnalysis,
        title: titleAnalysis,
        projects: projectsAnalysis,
        education: educationAnalysis,
        certifications: certificationsAnalysis,
        awards: awardsAnalysis,
        interests: interestsAnalysis,
        publications: publicationsAnalysis,
        overallSummary,
      };
    } catch (error) {
      throw new Error(
        `Failed to analyze single resume: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`
      );
    }
  }

  /**
   * Analyzes multiple resumes against a single job posting.
   *
   * This function processes multiple resumes individually and returns
   * an array of complete analysis results, maintaining the order of input resumes.
   *
   * @param resumes - Array of resumes to analyze
   * @param job - Job posting to match against
   * @param options - Optional configuration for all analysis types
   * @returns Promise resolving to array of resume analysis results
   *
   * @example
   * ```typescript
   * const orchestrator = new ResumeOrchestrator(mistralModel);
   * const analyses = await orchestrator.analyzeMultipleResumes(
   *   [resume1, resume2, resume3],
   *   jobPosting,
   *   { skills: { confidenceThreshold: 2 } }
   * );
   *
   * // Sort by overall score
   * const ranked = analyses.sort((a, b) =>
   *   b.overallSummary.totalScore - a.overallSummary.totalScore
   * );
   *
   * console.log(`Best candidate score: ${ranked[0].overallSummary.totalScore}`);
   * ```
   */
  public async analyzeMultipleResumes(
    resumes: Resume[],
    job: Job,
    options: AnalysisOptions = {}
  ): Promise<AnalysisResult[]> {
    if (!resumes || resumes.length === 0) {
      throw new Error('At least one resume is required');
    }

    // Process resumes sequentially to avoid overwhelming the AI model
    const results: AnalysisResult[] = [];

    for (const resume of resumes) {
      try {
        const analysis = await this.analyzeResume(resume, job, options);
        results.push(analysis);
      } catch (error) {
        console.warn(
          `Failed to analyze resume, skipping: ${
            error instanceof Error ? error.message : 'Unknown error'
          }`
        );
        // Continue with other resumes rather than failing completely
      }
    }

    return results;
  }
}
