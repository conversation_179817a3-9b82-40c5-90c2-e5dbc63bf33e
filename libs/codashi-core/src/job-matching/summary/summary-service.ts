import { ScoringService } from '../scoring/scoring-service';
import { AnalysisResult, OverallSummary } from '../types';
export class SummaryService {
  private readonly weights = {
    EXPERIENCE: 0.25,
    SKILLS: 0.25,
    TITLE: 0.1,
    PROJECTS: 0.1,
    EDUCATION: 0.08,
    CERTIFICATIONS: 0.08,
    PUBLICATIONS: 0.08,
    AWARDS: 0.03,
    INTERESTS: 0.03,
  };

  private scoringService: ScoringService;

  constructor(scoringService: ScoringService) {
    this.scoringService = scoringService;
  }

  public calculateOverallSummary(
    experienceAnalysis: AnalysisResult['experience'],
    skillsAnalysis: AnalysisResult['skills'],
    titleAnalysis: AnalysisResult['title'],
    projectsAnalysis: AnalysisResult['projects'],
    educationAnalysis: AnalysisResult['education'],
    certificationsAnalysis: AnalysisResult['certifications'],
    awardsAnalysis: AnalysisResult['awards'],
    interestsAnalysis: AnalysisResult['interests'],
    publicationsAnalysis: AnalysisResult['publications']
  ): OverallSummary {
    const experienceScore =
      this.scoringService.calculateExperienceScore(experienceAnalysis);
    const skillsScore = skillsAnalysis.summary.coveragePercentage;
    const titleScore = titleAnalysis.confidence * 100;
    const projectsScore =
      this.scoringService.calculateProjectsScore(projectsAnalysis);
    const educationScore =
      this.scoringService.calculateEducationScore(educationAnalysis);
    const certificationsScore = this.scoringService.calculateCertificationScore(
      certificationsAnalysis
    );
    const awardsScore = this.scoringService.calculateAwardScore(awardsAnalysis);
    const interestsScore =
      this.scoringService.calculateInterestScore(interestsAnalysis);
    const publicationsScore =
      this.scoringService.calculatePublicationsScore(publicationsAnalysis);

    const totalScore = Math.round(
      experienceScore * this.weights.EXPERIENCE +
        skillsScore * this.weights.SKILLS +
        titleScore * this.weights.TITLE +
        projectsScore * this.weights.PROJECTS +
        educationScore * this.weights.EDUCATION +
        certificationsScore * this.weights.CERTIFICATIONS +
        publicationsScore * this.weights.PUBLICATIONS +
        awardsScore * this.weights.AWARDS +
        interestsScore * this.weights.INTERESTS
    );

    const matchQuality = this.calculateMatchQuality(
      experienceScore,
      skillsScore,
      titleAnalysis.confidence,
      projectsScore,
      educationScore,
      certificationsScore,
      awardsScore,
      interestsScore,
      publicationsScore
    );

    return {
      totalScore,
      matchQuality,
    };
  }

  private calculateMatchQuality(
    experienceScore: number,
    skillsCoverage: number,
    titleConfidence: number,
    projectsScore: number,
    educationScore: number,
    certificationsScore: number,
    awardsScore: number,
    interestsScore: number,
    publicationsScore: number
  ): 'poor' | 'fair' | 'good' | 'excellent' {
    const overallScore =
      experienceScore * this.weights.EXPERIENCE +
      skillsCoverage * this.weights.SKILLS +
      titleConfidence * 100 * this.weights.TITLE +
      projectsScore * this.weights.PROJECTS +
      educationScore * this.weights.EDUCATION +
      certificationsScore * this.weights.CERTIFICATIONS +
      awardsScore * this.weights.AWARDS +
      publicationsScore * this.weights.PUBLICATIONS +
      interestsScore * this.weights.INTERESTS;

    if (overallScore >= 80) return 'excellent';
    if (overallScore >= 65) return 'good';
    if (overallScore >= 45) return 'fair';

    return 'poor';
  }
}
