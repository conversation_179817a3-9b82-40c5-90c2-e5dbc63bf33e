import type { Resume } from '../../entities/resume';
import type { InterestItem } from './types';

/**
 * Extracts interests from a single resume without consolidation.
 */
export class InterestExtractor {
  extractInterests(resume: Resume): InterestItem[] {
    const interests: InterestItem[] = [];

    const interestsSection = resume.sections.find(
      (s) => s.name === 'interests'
    );
    if (!interestsSection || !interestsSection.items) return interests;

    for (const i of interestsSection.items) {
      const interest: InterestItem = {
        ...i,
        id: this.generateInterestId(i.name),
      };
      interests.push(interest);
    }

    return interests;
  }

  private generateInterestId(name?: string | null): string {
    const normalized = `${this.normalize(name)}`;
    return normalized.replace(/[^\w-]/g, '').substring(0, 50);
  }

  private normalize(v?: string | null): string {
    if (!v) return '';
    return v
      .toLowerCase()
      .trim()
      .replace(/[^\w\s-]/g, '') // allow hyphens
      .replace(/\s+/g, ' ');
  }
}
