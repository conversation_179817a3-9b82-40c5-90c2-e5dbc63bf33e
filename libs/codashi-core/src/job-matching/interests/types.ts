import { z } from 'zod';
import type { ResumeEntity } from '../../entities/resume';

/**
 * Represents an interest item extracted from a single resume
 */
export type InterestItem = ResumeEntity<'interest'> & {
  // artificially generated stable id
  id: string;
};

/**
 * Configuration options for single resume interests analysis
 */
export type InterestAnalysisOptions = {
  /** Timeout in milliseconds for AI operations */
  timeoutMs?: number;
  /** Batch size for LLM calls (number of interests per call) */
  batchSize?: number;
};

/**
 * Complete analysis result for single resume interests matching
 */
export type InterestAnalysis = {
  /** All interests from the single resume */
  interests: InterestItem[];
  /** Interests with relevance scores */
  scoredInterests: InterestScore[];
  /** Summary statistics */
  summary: {
    /** Total number of interests */
    totalInterests: number;
    /** Average relevance score across all interests */
    averageRelevanceScore: number;
    /** Number of highly relevant interests (score >= 4) */
    highRelevanceCount: number;
  };
};

/**
 * Schema for AI-powered single resume interests scoring results
 */
export const interestScoreSchema = z.object({
  scores: z.array(
    z.object({
      interestId: z.string(),
      relevanceScore: z.number().min(1).max(5),
      reasoning: z.string().max(300),
    })
  ),
});

export type InterestScoreResult = z.infer<typeof interestScoreSchema>;

export type InterestScore = z.infer<
  typeof interestScoreSchema
>['scores'][number];
