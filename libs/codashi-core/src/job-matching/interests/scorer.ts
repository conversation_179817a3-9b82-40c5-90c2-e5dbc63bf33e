import type { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { StructuredOutputParser } from '@langchain/core/output_parsers';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import { RunnableSequence } from '@langchain/core/runnables';

import type { Job } from '../../entities/job';
import { withTimeout } from '../../utils/common-utils';
import { formatJobContext } from '../utils';
import {
  INTEREST_SCORING_STANDALONE_PROMPT,
  formatInterestForPrompt,
} from './prompt-components';
import type {
  InterestAnalysisOptions,
  InterestItem,
  InterestScore,
  InterestScoreResult,
} from './types';
import { interestScoreSchema } from './types';

/**
 * Scores interests from a single resume against job requirements.
 */
export class InterestScorer {
  private readonly DEFAULT_BATCH_SIZE = 12;
  private readonly MAX_SAFE_TOKENS = 8000;

  constructor(private model: BaseChatModel) {}

  async scoreInterests(
    interests: InterestItem[],
    job: Job,
    options: InterestAnalysisOptions = {}
  ): Promise<InterestScore[]> {
    const batchSize = options.batchSize || this.DEFAULT_BATCH_SIZE;
    const timeoutMs = options.timeoutMs || 30000;

    const totalTokens = this.estimateTotalTokens(interests, job);
    if (totalTokens <= this.MAX_SAFE_TOKENS && interests.length <= batchSize) {
      return await this.scoreBatch(interests, job, timeoutMs);
    } else {
      return await this.scoreInBatches(interests, job, batchSize, timeoutMs);
    }
  }

  private async scoreBatch(
    interests: InterestItem[],
    job: Job,
    timeoutMs: number
  ): Promise<InterestScore[]> {
    const prompt = ChatPromptTemplate.fromTemplate(
      INTEREST_SCORING_STANDALONE_PROMPT
    );
    const parser = StructuredOutputParser.fromZodSchema(interestScoreSchema);
    const chain = RunnableSequence.from([prompt, this.model, parser]);

    const interestList = interests
      .map((i, index) => formatInterestForPrompt(i, index))
      .join('\n\n');

    const jobContext = formatJobContext(job);

    const result = await withTimeout(
      chain.invoke({
        ...jobContext,
        interestList,
        format_instructions: parser.getFormatInstructions(),
      }),
      timeoutMs,
      'Single resume interests scoring'
    );

    return this.mapResultsToScores(result, interests);
  }

  private async scoreInBatches(
    interests: InterestItem[],
    job: Job,
    batchSize: number,
    timeoutMs: number
  ): Promise<InterestScore[]> {
    const allScores: InterestScore[] = [];
    const failedBatches: InterestItem[][] = [];

    for (let i = 0; i < interests.length; i += batchSize) {
      const batch = interests.slice(i, i + batchSize);
      try {
        const batchScores = await this.scoreBatch(batch, job, timeoutMs);
        allScores.push(...batchScores);
      } catch (error) {
        console.error(
          `Failed to score interests batch starting at index ${i}:`,
          error
        );
        failedBatches.push(batch);
      }
    }

    if (failedBatches.length > 0) {
      console.warn(`Failed to score ${failedBatches.length} interest batches`);
    }

    return allScores;
  }

  private mapResultsToScores(
    result: InterestScoreResult,
    interests: InterestItem[]
  ): InterestScore[] {
    const scores: InterestScore[] = [];
    for (const scoreResult of result.scores) {
      const interest = interests.find((i) => i.id === scoreResult.interestId);
      if (interest) {
        scores.push({
          interestId: scoreResult.interestId,
          relevanceScore: scoreResult.relevanceScore,
          reasoning: scoreResult.reasoning,
        });
      }
    }
    return scores;
  }

  private estimateTotalTokens(interests: InterestItem[], job: Job): number {
    let tokenCount = 0;
    tokenCount += (job.title?.length || 0) / 4;
    tokenCount += (job.description?.length || 0) / 4;

    for (const i of interests) {
      tokenCount += (i.name?.length || 0) / 4;
      if (i.keywords) {
        tokenCount += i.keywords.reduce(
          (sum, keyword) => sum + (keyword?.length || 0) / 4,
          0
        );
      }
    }

    return Math.ceil(tokenCount);
  }
}
