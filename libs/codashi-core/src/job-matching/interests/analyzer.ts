import type { BaseChatModel } from '@langchain/core/language_models/chat_models';

import { isNonEmptyArray } from '@awe/core';
import type { Job } from '../../entities/job';
import type { Resume } from '../../entities/resume';
import {
  isAIUnavailableError,
  isTimeoutError,
  withTimeout,
} from '../../utils/common-utils';
import { InterestExtractor } from './extractor';
import { InterestScorer } from './scorer';
import type { InterestAnalysis, InterestAnalysisOptions } from './types';

/**
 * Analyzes interests relevance for a single resume against a job posting.
 */
export class InterestAnalyzer {
  private readonly model: BaseChatModel;

  constructor(model: BaseChatModel) {
    this.model = model;
  }

  async analyzeInterests(
    resume: Resume,
    job: Job,
    options: InterestAnalysisOptions = {}
  ): Promise<InterestAnalysis> {
    const finalOptions = {
      timeoutMs: 30000,
      batchSize: 12,
      ...options,
    } satisfies InterestAnalysisOptions;

    try {
      const extractor = new InterestExtractor();
      const interests = extractor.extractInterests(resume);

      if (!isNonEmptyArray(interests)) {
        return {
          interests: [],
          scoredInterests: [],
          summary: {
            totalInterests: 0,
            averageRelevanceScore: 0,
            highRelevanceCount: 0,
          },
        };
      }

      const scorer = new InterestScorer(this.model);
      const scoredInterests = await withTimeout(
        scorer.scoreInterests(interests, job, finalOptions),
        finalOptions.timeoutMs,
        'Single resume interests scoring'
      );

      const summary = this.calculateSummary(scoredInterests);

      return {
        interests,
        scoredInterests,
        summary,
      };
    } catch (error) {
      if (isAIUnavailableError(error)) {
        throw new InterestMatchError(
          'AI model is currently unavailable for interests analysis',
          'AI_UNAVAILABLE'
        );
      }

      if (isTimeoutError(error)) {
        throw new InterestMatchError('Interests analysis timed out', 'TIMEOUT');
      }

      throw new InterestMatchError(
        `Interests analysis failed: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        'INVALID_INPUT'
      );
    }
  }

  private calculateSummary(
    scored: InterestAnalysis['scoredInterests']
  ): InterestAnalysis['summary'] {
    const total = scored.length;

    if (total === 0) {
      return {
        totalInterests: 0,
        averageRelevanceScore: 0,
        highRelevanceCount: 0,
      };
    }

    const totalScore = scored.reduce((sum, s) => sum + s.relevanceScore, 0);
    const averageRelevanceScore = Math.round((totalScore / total) * 10) / 10;
    const highRelevanceCount = scored.filter(
      (s) => s.relevanceScore >= 4
    ).length;

    return {
      totalInterests: total,
      averageRelevanceScore,
      highRelevanceCount,
    };
  }
}

class InterestMatchError extends Error {
  constructor(
    message: string,
    public readonly code:
      | 'AI_UNAVAILABLE'
      | 'TIMEOUT'
      | 'INVALID_INPUT'
      | 'NO_INTERESTS'
  ) {
    super(message);
    this.name = 'InterestMatchError';
  }
}
