import { AnalysisResult } from '../types';

export class ScoringService {
  private calculateScoreFromRelevance(
    items: unknown[],
    averageRelevanceScore: number
  ): number {
    if (items.length === 0 || averageRelevanceScore === 0) {
      return 0;
    }
    return Math.round(((averageRelevanceScore - 1) / 4) * 100);
  }

  public calculateExperienceScore(
    experienceAnalysis: AnalysisResult['experience']
  ): number {
    return this.calculateScoreFromRelevance(
      experienceAnalysis.experiences,
      experienceAnalysis.summary.averageRelevanceScore
    );
  }

  public calculateProjectsScore(
    projectsAnalysis: AnalysisResult['projects']
  ): number {
    return this.calculateScoreFromRelevance(
      projectsAnalysis.projects,
      projectsAnalysis.summary.averageRelevanceScore
    );
  }

  public calculatePublicationsScore(
    publicationsAnalysis: AnalysisResult['publications']
  ): number {
    if (!publicationsAnalysis) {
      return 0;
    }
    return this.calculateScoreFromRelevance(
      publicationsAnalysis.publications,
      publicationsAnalysis.summary.averageRelevanceScore
    );
  }

  public calculateEducationScore(
    educationAnalysis: AnalysisResult['education']
  ): number {
    return this.calculateScoreFromRelevance(
      educationAnalysis.educations,
      educationAnalysis.summary.averageRelevanceScore
    );
  }

  public calculateCertificationScore(
    certificationsAnalysis: AnalysisResult['certifications']
  ): number {
    return this.calculateScoreFromRelevance(
      certificationsAnalysis.certifications,
      certificationsAnalysis.summary.averageRelevanceScore
    );
  }

  public calculateAwardScore(awardsAnalysis: AnalysisResult['awards']): number {
    return this.calculateScoreFromRelevance(
      awardsAnalysis.awards,
      awardsAnalysis.summary.averageRelevanceScore
    );
  }

  public calculateInterestScore(
    interestsAnalysis: AnalysisResult['interests']
  ): number {
    return this.calculateScoreFromRelevance(
      interestsAnalysis.interests,
      interestsAnalysis.summary.averageRelevanceScore
    );
  }
}
