import type { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { RunnableSequence } from '@langchain/core/runnables';
import { filter, map, pipe } from 'rambda';

import type { JobEntity } from '../../../entities/job';
import {
  isAIUnavailableError,
  isTimeoutError,
  withTimeout,
} from '../../../utils/common-utils';
import type {
  Skill,
  SkillMatchOptions,
  TransferableSkillMatch,
} from '../types';

import { type NonEmptyArray, isNonEmptyArray } from '@awe/core';
import { BaseExtractor } from './base-extractor';

/**
 * Internal interface for skill comparison pairs
 */
type SkillComparison = {
  resumeSkill: Skill;
  jobSkill: JobEntity<'skills'>;
};

/**
 * Handles batched extraction of transferable skills using AI.
 * This approach processes skills in smaller chunks for large inputs.
 */
export class BatchedExtractor extends BaseExtractor {
  // Configuration constants
  private readonly LARGE_BATCH_SIZE = 50;

  constructor(model: BaseChatModel) {
    super(model);
  }

  /**
   * Fallback method for large inputs that exceed token limits
   * Uses larger batches than the original implementation for better efficiency
   */
  async extractTransferableSkills(
    unmatchedResumeSkills: NonEmptyArray<Skill>,
    unmatchedJobSkills: NonEmptyArray<JobEntity<'skills'>>,
    options: SkillMatchOptions
  ): Promise<TransferableSkillMatch[]> {
    // Create skill comparison pairs
    const skillComparisons = this.createSkillComparisons(
      unmatchedResumeSkills,
      unmatchedJobSkills
    );

    if (!skillComparisons.length) {
      return [];
    }

    // Use larger batches for fallback (50 instead of 10)
    const batches: SkillComparison[][] = [];

    for (let i = 0; i < skillComparisons.length; i += this.LARGE_BATCH_SIZE) {
      batches.push(skillComparisons.slice(i, i + this.LARGE_BATCH_SIZE));
    }

    const allMatches: TransferableSkillMatch[] = [];

    for (const batch of filter(isNonEmptyArray)(batches)) {
      try {
        const batchMatches = await this.processBatch(batch, options);
        allMatches.push(...batchMatches);
      } catch (error) {
        if (isAIUnavailableError(error)) {
          console.warn(
            'AI model unavailable for transferable skill batch, skipping batch'
          );
        } else if (isTimeoutError(error)) {
          console.warn('Transferable skill batch timed out, skipping batch');
        } else {
          console.warn('Failed to process transferable skill batch:', error);
        }
        // Continue with other batches on failure
      }
    }

    return allMatches;
  }

  /**
   * Creates skill comparison pairs from unmatched skills with deduplication
   */
  private createSkillComparisons(
    resumeSkills: NonEmptyArray<Skill>,
    jobSkills: NonEmptyArray<JobEntity<'skills'>>
  ): SkillComparison[] {
    const comparisons: SkillComparison[] = [];
    const seenPairs = new Set<string>();

    for (const resumeSkill of resumeSkills) {
      for (const jobSkill of jobSkills) {
        // Create a normalized key for deduplication
        const pairKey = this.createSkillPairKey(
          resumeSkill.name,
          jobSkill.name
        );

        // Skip if we've already seen this skill pair
        if (seenPairs.has(pairKey)) {
          continue;
        }

        seenPairs.add(pairKey);
        comparisons.push({
          resumeSkill,
          jobSkill,
        });
      }
    }

    // No grouping or optimization: return all unique pairs
    return comparisons;
  }

  /**
   * Processes a batch of skill comparisons using AI
   */
  private async processBatch(
    batch: NonEmptyArray<SkillComparison>,
    options: SkillMatchOptions
  ): Promise<TransferableSkillMatch[]> {
    const resumeSkillsText = batch
      .map(
        ({ resumeSkill }) =>
          `- ${resumeSkill.name}${
            resumeSkill.keywords?.length
              ? ` (${resumeSkill.keywords.join(', ')})`
              : ''
          }`
      )
      .join('\n');

    const jobSkillsText = batch
      .map(
        ({ jobSkill }) =>
          `- ${jobSkill.name}${
            jobSkill.keywords?.length
              ? ` (${jobSkill.keywords.join(', ')})`
              : ''
          }`
      )
      .join('\n');

    const chain = RunnableSequence.from([
      this.promptTemplate,
      this.model,
      this.outputParser,
    ]);

    const chainOperation = chain.invoke({
      resumeSkills: resumeSkillsText,
      jobSkills: jobSkillsText,
      formatInstructions: this.outputParser.getFormatInstructions(),
    });

    const result = options.timeoutMs
      ? await withTimeout(
          chainOperation,
          options.timeoutMs,
          'Transferable skill AI analysis'
        )
      : await chainOperation;

    return pipe(
      result.matches,
      map((match) => {
        const resumeSkill = batch.find(
          (b) => b.resumeSkill.name === match.resumeSkill
        )?.resumeSkill;

        if (!resumeSkill) {
          return null;
        }

        return {
          jobSkill: match.jobSkill,
          resumeSkill: match.resumeSkill,
          confidenceRating: match.confidenceRating,
          reasoning: match.reasoning,
          source: resumeSkill.source,
        };
      }),
      filter(Boolean)
    );
  }

  /**
   * Creates a normalized key for skill pair deduplication
   */
  private createSkillPairKey(resumeSkill: string, jobSkill: string): string {
    // Normalize skill names for comparison (lowercase, trim)
    const normalizedResume = resumeSkill.toLowerCase().trim();
    const normalizedJob = jobSkill.toLowerCase().trim();

    // Create a consistent key regardless of order
    return `${normalizedResume}|${normalizedJob}`;
  }
}
