import type { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { StructuredOutputParser } from '@langchain/core/output_parsers';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import { z } from 'zod';

import { type NonEmptyArray } from '@awe/core';
import type { JobEntity } from '../../../entities/job';
import type {
  Skill,
  SkillMatchOptions,
  TransferableSkillMatch,
} from '../types';

/**
 * Schema for AI-powered transferable skill analysis
 */
export const transferabilityAnalysisSchema = z.object({
  matches: z.array(
    z.object({
      resumeSkill: z.string(),
      jobSkill: z.string(),
      confidenceRating: z.union([z.literal(1), z.literal(2), z.literal(3)]),
      reasoning: z.string().max(200),
    })
  ),
});

/**
 * Base class for transferable skill extraction
 */
export abstract class BaseExtractor {
  protected readonly outputParser: StructuredOutputParser<
    typeof transferabilityAnalysisSchema
  >;
  protected readonly promptTemplate: ChatPromptTemplate;

  constructor(protected readonly model: BaseChatModel) {
    this.outputParser = StructuredOutputParser.fromZodSchema(
      transferabilityAnalysisSchema
    );
    this.promptTemplate = this.createTransferabilityPrompt();
  }

  /**
   * Abstract method for extracting transferable skills
   * Must be implemented by subclasses
   */
  abstract extractTransferableSkills(
    unmatchedResumeSkills: NonEmptyArray<Skill>,
    unmatchedJobSkills: NonEmptyArray<JobEntity<'skills'>>,
    options: SkillMatchOptions
  ): Promise<TransferableSkillMatch[]>;

  /**
   * Creates the prompt template for transferable skill analysis
   */
  protected createTransferabilityPrompt(): ChatPromptTemplate {
    return ChatPromptTemplate.fromTemplate(`
You are an expert career counselor analyzing skill transferability between a candidate's resume and job requirements.

Your task is to comprehensively analyze ALL resume skills against ALL job requirements to identify transferable matches. You will receive the complete set of unmatched skills and should find all relevant transferable relationships in a single analysis.

**Analysis Criteria:**

1. **Technology Family Relationships**: Skills in the same technology family (e.g., React vs Vue, MySQL vs PostgreSQL) should have high transferability (confidence 2-3)

2. **Conceptual Similarity**: Skills that share similar concepts or problem-solving approaches (e.g., Java vs C#, AWS vs Azure) should have moderate to high transferability (confidence 2-3)

3. **Domain Knowledge Transfer**: Skills that require similar domain knowledge or thinking patterns (e.g., data analysis tools, testing frameworks) should have moderate transferability (confidence 1-2)

4. **Learning Curve Consideration**: Skills that require significant learning or paradigm shifts should have lower confidence ratings (confidence 1-2)

**Confidence Rating Guidelines:**
- **3 (High)**: Very similar technologies, minimal learning curve, direct transferability
- **2 (Medium)**: Related technologies, moderate learning curve, good transferability with some adaptation
- **1 (Low)**: Conceptually related but requires significant learning, basic transferability

**Complete Resume Skills to Analyze:**
{resumeSkills}

**Complete Job Requirements to Match Against:**
{jobSkills}

**Instructions:**
- Analyze EVERY resume skill against EVERY job requirement
- Identify ALL transferable matches with confidence ratings 1-3
- Provide clear, concise reasoning (max 200 characters) for each match
- Focus on the most relevant and valuable transferable relationships
- Consider the candidate's learning potential and skill adaptation capabilities

{formatInstructions}
`);
  }
}
