import type { BaseChatModel } from '@langchain/core/language_models/chat_models';

import type { JobEntity } from '../../../entities/job';
import {
  isAIUnavailableError,
  isTimeoutError,
} from '../../../utils/common-utils';
import type {
  Skill,
  SkillMatchOptions,
  TransferableSkillMatch,
} from '../types';
import { BatchedExtractor } from './batched-extractor';
import { SingleExtractor } from './single-extractor';

type JobSkill = JobEntity<'skills'>;

/**
 * Main coordinator class that orchestrates transferable skill extraction.
 * Attempts single extraction first, then falls back to batched extraction if needed.
 */
export class TransferableSkillExtractor {
  private readonly singleExtraction: SingleExtractor;
  private readonly batchedExtraction: BatchedExtractor;

  // Configuration constants
  private readonly MAX_SAFE_TOKENS = 8000; // Conservative limit for most models

  constructor(private readonly model: BaseChatModel) {
    this.singleExtraction = new SingleExtractor(model);
    this.batchedExtraction = new BatchedExtractor(model);
  }

  /**
   * Analyzes transferable skills between unmatched resume skills and job requirements
   *
   * @param unmatchedResumeSkills - Resume skills that didn't have direct matches
   * @param unmatchedJobSkills - Job skills that didn't have direct matches
   * @param options - Configuration options for the analysis
   * @returns Promise resolving to array of transferable skill matches
   */
  async analyzeTransferableSkills(
    unmatchedResumeSkills: [Skill, ...Skill[]],
    unmatchedJobSkills: [JobSkill, ...JobSkill[]],
    options: SkillMatchOptions = {}
  ): Promise<TransferableSkillMatch[]> {
    // Estimate token count to determine processing strategy
    const estimatedTokens = this.estimateTokenCount(
      unmatchedResumeSkills,
      unmatchedJobSkills
    );

    let allMatches: TransferableSkillMatch[] = [];

    try {
      if (estimatedTokens <= this.MAX_SAFE_TOKENS) {
        // Use single-call approach for efficiency
        allMatches = await this.singleExtraction.extractTransferableSkills(
          unmatchedResumeSkills,
          unmatchedJobSkills,
          options
        );
      } else {
        // Fallback to batching for large inputs
        console.warn(
          `Large input detected (${estimatedTokens} tokens), falling back to batch processing`
        );

        allMatches = await this.batchedExtraction.extractTransferableSkills(
          unmatchedResumeSkills,
          unmatchedJobSkills,
          options
        );
      }
    } catch (error) {
      if (isAIUnavailableError(error)) {
        console.warn(
          'AI model unavailable for transferable skill analysis, returning empty results'
        );
      } else if (isTimeoutError(error)) {
        console.warn(
          'Transferable skill analysis timed out, returning empty results'
        );
      } else {
        console.warn('Failed to process transferable skills:', error);
      }
      // Return empty results on failure
      return [];
    }

    // Apply confidence threshold and limit if specified
    return this.filterAndLimitResults(allMatches, options);
  }

  /**
   * Estimates the token count for the input skills
   */
  private estimateTokenCount(
    resumeSkills: [Skill, ...Skill[]],
    jobSkills: [JobSkill, ...JobSkill[]]
  ): number {
    // Rough estimation: ~3 tokens per skill name + ~5 tokens per keyword set + prompt overhead
    const resumeTokens = resumeSkills.reduce((total, skill) => {
      const nameTokens = Math.ceil(skill.name.length / 4); // ~4 chars per token
      const keywordTokens = skill.keywords?.length
        ? skill.keywords.length * 2
        : 0;
      return total + nameTokens + keywordTokens;
    }, 0);

    const jobTokens = jobSkills.reduce((total, skill) => {
      const nameTokens = Math.ceil(skill.name.length / 4);
      const keywordTokens = skill.keywords?.length
        ? skill.keywords.length * 2
        : 0;
      return total + nameTokens + keywordTokens;
    }, 0);

    const promptOverhead = 800; // Estimated prompt template tokens
    return resumeTokens + jobTokens + promptOverhead;
  }

  /**
   * Filters results by confidence threshold and applies limits
   */
  private filterAndLimitResults(
    matches: TransferableSkillMatch[],
    options: SkillMatchOptions
  ): TransferableSkillMatch[] {
    let filteredMatches = matches;

    // Apply confidence threshold
    if (options.confidenceThreshold) {
      filteredMatches = filteredMatches.filter((match) =>
        options.confidenceThreshold
          ? match.confidenceRating >= options.confidenceThreshold
          : true
      );
    }

    // Sort by confidence rating (highest first) and limit if specified
    filteredMatches.sort((a, b) => b.confidenceRating - a.confidenceRating);

    if (options.maxTransferableSkills) {
      filteredMatches = filteredMatches.slice(0, options.maxTransferableSkills);
    }

    return filteredMatches;
  }
}
