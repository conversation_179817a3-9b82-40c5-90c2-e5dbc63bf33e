import { RunnableSequence } from '@langchain/core/runnables';
import { filter, map, pipe } from 'rambda';

import { NonEmptyArray } from '@awe/core';
import type { Job } from '../../../entities/job';
import { withTimeout } from '../../../utils/common-utils';
import type {
  Skill,
  SkillMatchOptions,
  TransferableSkillMatch,
} from '../types';
import { BaseExtractor } from './base-extractor';

type JobSkill = NonNullable<Job['skills']>[number];

/**
 * Handles single-call extraction of transferable skills using AI.
 * This approach processes all skills in one LLM call for efficiency.
 */
export class SingleExtractor extends BaseExtractor {
  /**
   * Processes all skill comparisons in a single AI call
   */
  async extractTransferableSkills(
    unmatchedResumeSkills: NonEmptyArray<Skill>,
    unmatchedJobSkills: NonEmptyArray<JobSkill>,
    options: SkillMatchOptions
  ): Promise<TransferableSkillMatch[]> {
    const resumeSkillsText = unmatchedResumeSkills
      .map(
        (skill) =>
          `- ${skill.name}${
            skill.keywords?.length ? ` (${skill.keywords.join(', ')})` : ''
          }`
      )
      .join('\n');

    const jobSkillsText = unmatchedJobSkills
      .map(
        (skill) =>
          `- ${skill.name}${
            skill.keywords?.length ? ` (${skill.keywords.join(', ')})` : ''
          }`
      )
      .join('\n');

    const chain = RunnableSequence.from([
      this.promptTemplate,
      this.model,
      this.outputParser,
    ]);

    const chainOperation = chain.invoke({
      resumeSkills: resumeSkillsText,
      jobSkills: jobSkillsText,
      formatInstructions: this.outputParser.getFormatInstructions(),
    });

    const result = options.timeoutMs
      ? await withTimeout(
          chainOperation,
          options.timeoutMs,
          'Transferable skill AI analysis'
        )
      : await chainOperation;

    return pipe(
      result.matches,
      map((match) => {
        const resumeSkill = unmatchedResumeSkills.find(
          (skill) => skill.name === match.resumeSkill
        );

        if (!resumeSkill) {
          return null;
        }

        return {
          jobSkill: match.jobSkill,
          resumeSkill: match.resumeSkill,
          confidenceRating: match.confidenceRating,
          reasoning: match.reasoning,
          source: resumeSkill.source,
        };
      }),
      filter(Boolean)
    );
  }
}
