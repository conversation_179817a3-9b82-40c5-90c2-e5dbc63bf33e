import { z } from 'zod';

/**
 * Represents a skill extracted from a single resume
 */
export interface Skill {
  name: string;
  level?: string | null;
  keywords: string[];
  source: 'explicit' | 'work_experience' | 'projects' | 'education';
  sourceSection?: string; // e.g., "work_experience[0]", "projects[1]"
}

/**
 * Represents a direct skill match from a single resume
 */
export interface DirectSkillMatch {
  jobSkill: string;
  resumeSkill: string;
  matchType: 'exact' | 'synonym' | 'keyword';
  source: 'explicit' | 'work_experience' | 'projects' | 'education';
  sourceSection?: string;
}

/**
 * Represents a transferable skill match from a single resume
 */
export interface TransferableSkillMatch {
  jobSkill: string;
  resumeSkill: string;
  confidenceRating: 1 | 2 | 3;
  reasoning: string;
  source: 'explicit' | 'work_experience' | 'projects' | 'education';
  sourceSection?: string;
}

/**
 * Represents a missing skill that the single resume doesn't have
 */
export interface MissingSkill {
  name: string;
  level?: string | null;
  keywords?: string[] | null;
  category?: string;
}

/**
 * Main result interface for single-resume skill analysis
 */
export interface SkillAnalysis {
  resumeSkills: Skill[];
  directMatches: DirectSkillMatch[];
  transferableSkills: TransferableSkillMatch[];
  missingSkills: MissingSkill[];
  summary: {
    totalJobSkills: number;
    totalResumeSkills: number;
    directMatchCount: number;
    transferableMatchCount: number;
    missingSkillCount: number;
    coveragePercentage: number;
  };
}

/**
 * Configuration options for single-resume skill analysis
 */
export interface SkillAnalysisOptions {
  maxTransferableSkills?: number; // Limit AI processing
  confidenceThreshold?: 1 | 2 | 3; // Minimum confidence to include
  timeoutMs?: number; // Timeout for AI operations in milliseconds
  includeSourceDetails?: boolean; // Include source section information
}

/**
 * Error class for single-resume skill analysis failures
 */
export class SkillMatchError extends Error {
  constructor(message: string, public readonly cause?: unknown) {
    super(message);
    this.name = 'SkillMatchError';
  }
}

// Zod schemas for AI-powered skill extraction and analysis

/**
 * Schema for skill extraction from text
 */
export const skillExtractionSchema = z.object({
  skills: z
    .array(z.string())
    .describe(
      'Array of technical skills, tools, frameworks, programming languages, and technologies found in the text'
    ),
});

/**
 * Schema for transferable skill analysis
 */
export const transferabilityAnalysisSchema = z.object({
  matches: z.array(
    z.object({
      resumeSkill: z.string(),
      jobSkill: z.string(),
      confidenceRating: z.union([z.literal(1), z.literal(2), z.literal(3)]),
      reasoning: z.string().max(200),
    })
  ),
});

/**
 * Schema for synonym detection
 */
export const synonymDetectionSchema = z.object({
  matches: z.array(
    z.object({
      resumeSkill: z.string(),
      jobSkill: z.string(),
      isSynonym: z.boolean(),
      reasoning: z.string().max(100),
    })
  ),
});

export interface SkillMatchOptions {
  includeSourceResume?: boolean; // For introspection
  maxTransferableSkills?: number; // Limit AI processing
  confidenceThreshold?: 1 | 2 | 3; // Minimum confidence to include
  timeoutMs?: number; // Timeout for AI operations in milliseconds
}
