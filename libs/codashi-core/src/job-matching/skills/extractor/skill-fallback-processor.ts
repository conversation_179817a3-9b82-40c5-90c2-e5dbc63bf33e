import type { Resume } from '../../../entities/resume';
import type { Skill, SkillAnalysisOptions } from '../types';

import { SkillTextExtractor } from './skill-text-extractor';
import { SkillSectionProcessor } from './skill-section-processor';

/**
 * Handles fallback processing for oversized content
 * Uses individual processing when consolidated text exceeds token limits
 */
export class SkillFallbackProcessor {
  /**
   * Fallback method using individual processing for oversized content
   * Uses the original approach when consolidated text exceeds token limits
   * @param resume - Resume to process
   * @param options - Analysis options
   * @param textExtractor - Text extractor instance
   * @param sectionProcessor - Section processor instance
   * @returns Promise resolving to array of skills
   */
  async extractSkillsUsingFallback(
    resume: Resume,
    options: SkillAnalysisOptions,
    textExtractor: SkillTextExtractor,
    sectionProcessor: SkillSectionProcessor
  ): Promise<Skill[]> {
    const allSkills: Skill[] = [];

    // Process work experience using original individual approach
    const workExperienceSection = resume.sections?.find(
      (section) => section.name === 'work'
    );

    if (workExperienceSection?.items) {
      for (const [
        expIndex,
        experience,
      ] of workExperienceSection.items.entries()) {
        const workSkills =
          await sectionProcessor.extractSkillsFromWorkExperience(
            experience,
            expIndex,
            options,
            textExtractor
          );
        allSkills.push(...workSkills);
      }
    }

    // Process projects using original individual approach
    const projectsSection = resume.sections?.find(
      (section) => section.name === 'projects'
    );

    if (projectsSection?.items) {
      for (const [projIndex, project] of projectsSection.items.entries()) {
        const projectSkills = await sectionProcessor.extractSkillsFromProject(
          project,
          projIndex,
          options,
          textExtractor
        );
        allSkills.push(...projectSkills);
      }
    }

    // Process education using original individual approach
    const educationSection = resume.sections?.find(
      (section) => section.name === 'education'
    );

    if (educationSection?.items) {
      for (const [eduIndex, education] of educationSection.items.entries()) {
        const educationSkills =
          await sectionProcessor.extractSkillsFromEducation(
            education,
            eduIndex,
            options,
            textExtractor
          );
        allSkills.push(...educationSkills);
      }
    }

    return allSkills;
  }
}
