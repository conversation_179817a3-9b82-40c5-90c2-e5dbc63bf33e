import type { BaseChatModel } from '@langchain/core/language_models/chat_models';

import { SkillConsolidator } from './skill-consolidator';
import { SkillDeduplicator } from './skill-deduplicator';
import { SkillExtractor } from './skill-extractor';
import { SkillFallbackProcessor } from './skill-fallback-processor';
import { SkillSectionProcessor } from './skill-section-processor';
import { SkillTextExtractor } from './skill-text-extractor';

/**
 * Factory class for creating skill extraction components
 * Provides a unified interface for creating all skill extraction components
 */
export class SkillExtractorFactory {
  private model: BaseChatModel;

  constructor(model: BaseChatModel) {
    this.model = model;
  }

  /**
   * Creates a new SkillExtractor instance
   * @returns New SkillExtractor instance
   */
  createExtractor(): SkillExtractor {
    return new SkillExtractor(this.model);
  }

  /**
   * Creates a new SkillConsolidator instance
   * @returns New SkillConsolidator instance
   */
  createConsolidator(): SkillConsolidator {
    return new SkillConsolidator(this.model);
  }

  /**
   * Creates a new SkillDeduplicator instance
   * @returns New SkillDeduplicator instance
   */
  createDeduplicator(): SkillDeduplicator {
    return new SkillDeduplicator();
  }

  /**
   * Creates a new SkillTextExtractor instance
   * @returns New SkillTextExtractor instance
   */
  createTextExtractor(): SkillTextExtractor {
    return new SkillTextExtractor(this.model);
  }

  /**
   * Creates a new SkillSectionProcessor instance
   * @returns New SkillSectionProcessor instance
   */
  createSectionProcessor(): SkillSectionProcessor {
    return new SkillSectionProcessor();
  }

  /**
   * Creates a new SkillFallbackProcessor instance
   * @returns New SkillFallbackProcessor instance
   */
  createFallbackProcessor(): SkillFallbackProcessor {
    return new SkillFallbackProcessor();
  }
}
