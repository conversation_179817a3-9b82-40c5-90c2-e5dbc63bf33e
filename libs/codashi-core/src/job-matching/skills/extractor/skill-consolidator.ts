import type { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { StructuredOutputParser } from '@langchain/core/output_parsers';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import { RunnableSequence } from '@langchain/core/runnables';

import type { Resume } from '../../../entities/resume';
import { withTimeout } from '../../../utils/common-utils';
import type { Skill, SkillAnalysisOptions } from '../types';
import { skillExtractionSchema } from '../types';

/**
 * Consolidates resume text for AI skill extraction
 * Handles text collection and token estimation for efficient processing
 */
export class SkillConsolidator {
  private model: BaseChatModel;
  private readonly MAX_SAFE_TOKENS = 6000; // Conservative limit for consolidated text

  constructor(model: BaseChatModel) {
    this.model = model;
  }

  /**
   * Consolidates resume text for AI processing
   * @param resume - Resume to process
   * @returns Consolidated text and content flag
   */
  collectTextForExtraction(resume: Resume): {
    consolidatedText: string;
    hasContent: boolean;
  } {
    const textSections: string[] = [];

    // Collect work experience text
    const workSection = resume.sections.find(
      (section) => section.name === 'work'
    );
    if (workSection?.items) {
      const workTexts: string[] = [];
      for (const experience of workSection.items) {
        const experienceTexts: string[] = [];

        if (experience.description?.trim()) {
          experienceTexts.push(
            `${experience.name} (${experience.position}): ${experience.description}`
          );
        }

        if (experience.highlights?.length) {
          const highlights = experience.highlights
            .filter((h) => h.trim())
            .join(' | ');
          if (highlights) {
            experienceTexts.push(`Highlights: ${highlights}`);
          }
        }

        if (experienceTexts.length > 0) {
          workTexts.push(experienceTexts.join('\n'));
        }
      }

      if (workTexts.length > 0) {
        textSections.push(`WORK_EXPERIENCE:\n${workTexts.join('\n\n')}`);
      }
    }

    // Collect projects text
    const projectsSection = resume.sections.find(
      (section) => section.name === 'projects'
    );
    if (projectsSection?.items) {
      const projectTexts: string[] = [];
      for (const project of projectsSection.items) {
        if (project.description?.trim()) {
          projectTexts.push(`${project.name}: ${project.description}`);
        }
      }

      if (projectTexts.length > 0) {
        textSections.push(`PROJECTS:\n${projectTexts.join('\n')}`);
      }
    }

    // Collect education text
    const educationSection = resume.sections.find(
      (section) => section.name === 'education'
    );
    if (educationSection?.items) {
      const courseTexts: string[] = [];
      for (const education of educationSection.items) {
        if (education.courses?.length) {
          const courses = education.courses
            .filter((course) => course.trim())
            .join(' | ');
          if (courses) {
            courseTexts.push(courses);
          }
        }
      }

      if (courseTexts.length > 0) {
        textSections.push(`EDUCATION:\nCourses: ${courseTexts.join(' | ')}`);
      }
    }

    const consolidatedText = textSections.join('\n\n');
    return {
      consolidatedText,
      hasContent: consolidatedText.trim().length > 0,
    };
  }

  /**
   * Estimates token count for text (rough approximation: 4 chars per token)
   * @param text - Text to estimate tokens for
   * @returns Estimated token count
   */
  estimateTokenCount(text: string): number {
    return Math.ceil(text.length / 4);
  }

  /**
   * Extracts skills from consolidated text using a single AI call
   * Maps extracted skills to appropriate source sections
   * @param consolidatedText - Text to extract skills from
   * @param options - Analysis options
   * @returns Promise resolving to array of skills
   */
  async extractSkillsFromConsolidatedText(
    consolidatedText: string,
    options: SkillAnalysisOptions
  ): Promise<Skill[]> {
    if (!consolidatedText.trim()) {
      return [];
    }

    try {
      const prompt = ChatPromptTemplate.fromTemplate(`
Extract technical skills, tools, frameworks, programming languages, and technologies from the following resume sections.
Focus on concrete, specific technologies rather than soft skills or general concepts.

The text contains multiple sections from a resume (WORK_EXPERIENCE, PROJECTS, EDUCATION).
Extract all relevant technical skills from across all sections.

Resume Content:
{text}

{format_instructions}
      `);

      const parser = StructuredOutputParser.fromZodSchema(
        skillExtractionSchema
      );
      const chain = RunnableSequence.from([prompt, this.model, parser]);

      const operation = chain.invoke({
        text: consolidatedText,
        format_instructions: parser.getFormatInstructions(),
      });

      const result = options.timeoutMs
        ? await withTimeout(
            operation,
            options.timeoutMs,
            'Consolidated skill extraction'
          )
        : await operation;

      const extractedSkills = result.skills || [];

      // Map skills to source sections based on content analysis
      return this.mapSkillsToSources(
        extractedSkills,
        consolidatedText,
        options
      );
    } catch (error) {
      console.warn(
        'Failed to extract skills using consolidated AI approach, falling back to pattern matching:',
        error
      );
      return this.extractSkillsFromTextFallback(consolidatedText).map(
        (skill) => ({
          name: skill,
          level: null,
          keywords: [],
          source: 'work_experience' as const, // Default source for fallback
          sourceSection: options.includeSourceDetails
            ? 'consolidated_fallback'
            : undefined,
        })
      );
    }
  }

  /**
   * Maps extracted skills to appropriate source sections based on text analysis
   * @param skills - Skills to map
   * @param consolidatedText - Text to analyze
   * @param options - Analysis options
   * @returns Mapped skills with source information
   */
  private mapSkillsToSources(
    skills: string[],
    consolidatedText: string,
    options: SkillAnalysisOptions
  ): Skill[] {
    const mappedSkills: Skill[] = [];

    // Determine which sections are present in the consolidated text
    const hasWorkSection = consolidatedText.includes('WORK_EXPERIENCE:');
    const hasProjectsSection = consolidatedText.includes('PROJECTS:');
    const hasEducationSection = consolidatedText.includes('EDUCATION:');

    for (const skillName of skills) {
      // Default to work_experience as primary source, then projects, then education
      let source: Skill['source'] = 'work_experience';

      if (hasWorkSection) {
        source = 'work_experience';
      } else if (hasProjectsSection) {
        source = 'projects';
      } else if (hasEducationSection) {
        source = 'education';
      }

      mappedSkills.push({
        name: skillName,
        level: null,
        keywords: [],
        source,
        sourceSection: options.includeSourceDetails
          ? 'consolidated_extraction'
          : undefined,
      });
    }

    return mappedSkills;
  }

  /**
   * Fallback skill extraction using pattern matching
   * @param text - Text to extract skills from
   * @returns Array of extracted skills
   */
  private extractSkillsFromTextFallback(text: string): string[] {
    const skillPatterns = [
      // Programming languages
      /\b(JavaScript|TypeScript|Python|Java|C\+\+|C#|PHP|Ruby|Go|Rust|Swift|Kotlin|Scala)\b/gi,
      // Frameworks and libraries
      /\b(React|Angular|Vue|Node\.js|Express|Django|Flask|Spring|Laravel|Rails)\b/gi,
      // Databases
      /\b(MySQL|PostgreSQL|MongoDB|Redis|SQLite|Oracle|SQL Server)\b/gi,
      // Cloud platforms
      /\b(AWS|Azure|GCP|Google Cloud|Heroku|Vercel|Netlify)\b/gi,
      // Tools
      /\b(Git|Docker|Kubernetes|Jenkins|Terraform|Ansible)\b/gi,
    ];

    const skills = new Set<string>();
    for (const pattern of skillPatterns) {
      const matches = text.match(pattern);
      if (matches) {
        matches.forEach((match) => skills.add(match));
      }
    }

    return Array.from(skills);
  }
}
