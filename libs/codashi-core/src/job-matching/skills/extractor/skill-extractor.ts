import type { BaseChatModel } from '@langchain/core/language_models/chat_models';

import type { Resume } from '../../../entities/resume';
import type { Skill, SkillAnalysisOptions } from '../types';

import { SkillExtractorFactory } from './skill-extractor-factory';

/**
 * Extracts skills from a single resume without consolidation logic.
 * Processes exactly one resume and extracts skills from all sections.
 */
export class SkillExtractor {
  private extractorFactory: SkillExtractorFactory;

  constructor(model: BaseChatModel) {
    this.extractorFactory = new SkillExtractorFactory(model);
  }

  /**
   * Extracts all skills from a single resume
   *
   * @param resume - Single resume to extract skills from
   * @param options - Configuration options
   * @returns Promise resolving to array of skills with source information
   */
  async extractSkills(
    resume: Resume,
    options: SkillAnalysisOptions = {}
  ): Promise<Skill[]> {
    const allSkills: Skill[] = [];

    // Process explicit skills section (no AI needed)
    const skillsSection = resume.sections.find(
      (section) => section.name === 'skills'
    );

    if (skillsSection?.items) {
      for (const [itemIndex, item] of skillsSection.items.entries()) {
        allSkills.push({
          name: item.name,
          level: item.level || null,
          keywords: item.keywords || [],
          source: 'explicit',
          sourceSection: options.includeSourceDetails
            ? `skills[${itemIndex}]`
            : undefined,
        });
      }
    }

    // Process project keywords directly (no AI needed)
    const projectsSection = resume.sections?.find(
      (section) => section.name === 'projects'
    );

    if (projectsSection?.items) {
      for (const [projIndex, project] of projectsSection.items.entries()) {
        if (project.keywords) {
          allSkills.push(
            ...project.keywords.map((keyword) => ({
              name: keyword,
              level: null,
              keywords: [],
              source: 'projects' as const,
              sourceSection: options.includeSourceDetails
                ? `projects[${projIndex}].keywords`
                : undefined,
            }))
          );
        }
      }
    }

    // Use consolidated AI extraction for text-based content
    const consolidator = this.extractorFactory.createConsolidator();
    const { consolidatedText, hasContent } =
      consolidator.collectTextForExtraction(resume);

    if (hasContent) {
      const estimatedTokens = consolidator.estimateTokenCount(consolidatedText);

      if (estimatedTokens <= 6000) {
        // Use consolidated approach for efficiency
        const consolidatedSkills =
          await consolidator.extractSkillsFromConsolidatedText(
            consolidatedText,
            options
          );
        allSkills.push(...consolidatedSkills);
      } else {
        // Fallback to individual processing for oversized content
        console.warn(
          `Large resume content detected (${estimatedTokens} tokens), falling back to individual processing`
        );

        const fallbackProcessor =
          this.extractorFactory.createFallbackProcessor();
        const textExtractor = this.extractorFactory.createTextExtractor();
        const sectionProcessor = this.extractorFactory.createSectionProcessor();

        const fallbackSkills =
          await fallbackProcessor.extractSkillsUsingFallback(
            resume,
            options,
            textExtractor,
            sectionProcessor
          );
        allSkills.push(...fallbackSkills);
      }
    }

    const deduplicator = this.extractorFactory.createDeduplicator();
    return deduplicator.deduplicateSkills(allSkills);
  }
}
