import type { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { StructuredOutputParser } from '@langchain/core/output_parsers';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import { RunnableSequence } from '@langchain/core/runnables';

import { withTimeout } from '../../../utils/common-utils';
import type { SkillAnalysisOptions } from '../types';
import { skillExtractionSchema } from '../types';

/**
 * Handles text-based skill extraction using AI
 * Provides both AI-based and fallback pattern matching approaches
 */
export class SkillTextExtractor {
  private model: BaseChatModel;

  constructor(model: BaseChatModel) {
    this.model = model;
  }

  /**
   * Extracts skills from free text using AI
   * @param text - Text to extract skills from
   * @param options - Analysis options
   * @returns Promise resolving to array of extracted skill names
   */
  async extractSkillsFromText(
    text: string,
    options: SkillAnalysisOptions
  ): Promise<string[]> {
    if (!text.trim()) {
      return [];
    }

    try {
      const prompt = ChatPromptTemplate.fromTemplate(`
Extract technical skills, tools, frameworks, programming languages, and technologies from the following text.
Focus on concrete, specific technologies rather than soft skills or general concepts.

Text: {text}

{format_instructions}
      `);

      const parser = StructuredOutputParser.fromZodSchema(
        skillExtractionSchema
      );
      const chain = RunnableSequence.from([prompt, this.model, parser]);

      const operation = chain.invoke({
        text,
        format_instructions: parser.getFormatInstructions(),
      });

      const result = options.timeoutMs
        ? await withTimeout(operation, options.timeoutMs, 'Skill extraction')
        : await operation;

      return result.skills || [];
    } catch (error) {
      console.warn(
        'Failed to extract skills using AI, falling back to pattern matching:',
        error
      );
      return this.extractSkillsFromTextFallback(text);
    }
  }

  /**
   * Fallback skill extraction using pattern matching
   * @param text - Text to extract skills from
   * @returns Array of extracted skill names
   */
  private extractSkillsFromTextFallback(text: string): string[] {
    const skillPatterns = [
      // Programming languages
      /\b(JavaScript|TypeScript|Python|Java|C\+\+|C#|PHP|Ruby|Go|Rust|Swift|Kotlin|Scala)\b/gi,
      // Frameworks and libraries
      /\b(React|Angular|Vue|Node\.js|Express|Django|Flask|Spring|Laravel|Rails)\b/gi,
      // Databases
      /\b(MySQL|PostgreSQL|MongoDB|Redis|SQLite|Oracle|SQL Server)\b/gi,
      // Cloud platforms
      /\b(AWS|Azure|GCP|Google Cloud|Heroku|Vercel|Netlify)\b/gi,
      // Tools
      /\b(Git|Docker|Kubernetes|Jenkins|Terraform|Ansible)\b/gi,
    ];

    const skills = new Set<string>();
    for (const pattern of skillPatterns) {
      const matches = text.match(pattern);
      if (matches) {
        matches.forEach((match) => skills.add(match));
      }
    }

    return Array.from(skills);
  }
}
