import type { Skill } from '../types';

/**
 * Handles skill deduplication and source prioritization
 * Ensures skills are unique while preserving the most informative source
 */
export class SkillDeduplicator {
  /**
   * Deduplicates skills while preserving the most informative source
   * @param skills - Array of skills to deduplicate
   * @returns Deduplicated array of skills
   */
  deduplicateSkills(skills: Skill[]): Skill[] {
    const skillMap = new Map<string, Skill>();

    for (const skill of skills) {
      const normalizedName = skill.name.toLowerCase().trim();
      const existing = skillMap.get(normalizedName);

      if (!existing) {
        skillMap.set(normalizedName, skill);
      } else {
        // Prefer explicit skills over extracted ones
        if (skill.source === 'explicit' && existing.source !== 'explicit') {
          skillMap.set(normalizedName, skill);
        }
        // Merge keywords if both have them
        else if (skill.keywords.length > 0 || existing.keywords.length > 0) {
          const mergedKeywords = [
            ...new Set([...existing.keywords, ...skill.keywords]),
          ];
          skillMap.set(normalizedName, {
            ...existing,
            keywords: mergedKeywords,
          });
        }
      }
    }

    return Array.from(skillMap.values());
  }
}
