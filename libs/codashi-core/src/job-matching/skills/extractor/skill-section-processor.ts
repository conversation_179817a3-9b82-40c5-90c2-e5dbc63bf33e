import type { ResumeEntity } from '../../../entities/resume';
import type { Skill, SkillAnalysisOptions } from '../types';

import { SkillTextExtractor } from './skill-text-extractor';

/**
 * Processes specific resume sections to extract skills
 * Handles different section types with appropriate processing logic
 */
export class SkillSectionProcessor {
  /**
   * Extracts skills from a work experience entry
   * @param experience - Work experience entity
   * @param index - Index of the experience in the section
   * @param options - Analysis options
   * @param textExtractor - Text extractor instance
   * @returns Promise resolving to array of skills
   */
  async extractSkillsFromWorkExperience(
    experience: ResumeEntity<'work'>,
    index: number,
    options: SkillAnalysisOptions,
    textExtractor: SkillTextExtractor
  ): Promise<Skill[]> {
    const skills: Skill[] = [];

    if (experience.description) {
      const extractedSkills = await textExtractor.extractSkillsFromText(
        experience.description,
        options
      );
      skills.push(
        ...extractedSkills.map((skill) => ({
          name: skill,
          level: null,
          keywords: [],
          source: 'work_experience' as const,
          sourceSection: options.includeSourceDetails
            ? `work_experience[${index}].description`
            : undefined,
        }))
      );
    }

    if (experience.highlights) {
      for (const [
        highlightIndex,
        highlight,
      ] of experience.highlights.entries()) {
        const extractedSkills = await textExtractor.extractSkillsFromText(
          highlight,
          options
        );

        skills.push(
          ...extractedSkills.map((skill) => ({
            name: skill,
            level: null,
            keywords: [],
            source: 'work_experience' as const,
            sourceSection: options.includeSourceDetails
              ? `work_experience[${index}].highlights[${highlightIndex}]`
              : undefined,
          }))
        );
      }
    }

    return skills;
  }

  /**
   * Extracts skills from a project entry
   * @param project - Project entity
   * @param index - Index of the project in the section
   * @param options - Analysis options
   * @param textExtractor - Text extractor instance
   * @returns Promise resolving to array of skills
   */
  async extractSkillsFromProject(
    project: ResumeEntity<'project'>,
    index: number,
    options: SkillAnalysisOptions,
    textExtractor: SkillTextExtractor
  ): Promise<Skill[]> {
    const skills: Skill[] = [];

    if (project.description) {
      const extractedSkills = await textExtractor.extractSkillsFromText(
        project.description,
        options
      );
      skills.push(
        ...extractedSkills.map((skill) => ({
          name: skill,
          level: null,
          keywords: [],
          source: 'projects' as const,
          sourceSection: options.includeSourceDetails
            ? `projects[${index}].description`
            : undefined,
        }))
      );
    }

    if (project.keywords) {
      skills.push(
        ...project.keywords.map((keyword) => ({
          name: keyword,
          level: null,
          keywords: [],
          source: 'projects' as const,
          sourceSection: options.includeSourceDetails
            ? `projects[${index}].keywords`
            : undefined,
        }))
      );
    }

    return skills;
  }

  /**
   * Extracts skills from an education entry
   * @param education - Education entity
   * @param index - Index of the education in the section
   * @param options - Analysis options
   * @param textExtractor - Text extractor instance
   * @returns Promise resolving to array of skills
   */
  async extractSkillsFromEducation(
    education: ResumeEntity<'education'>,
    index: number,
    options: SkillAnalysisOptions,
    textExtractor: SkillTextExtractor
  ): Promise<Skill[]> {
    const skills: Skill[] = [];

    if (education.courses) {
      for (const course of education.courses) {
        const extractedSkills = await textExtractor.extractSkillsFromText(
          course,
          options
        );
        skills.push(
          ...extractedSkills.map((skill) => ({
            name: skill,
            level: null,
            keywords: [],
            source: 'education' as const,
            sourceSection: options.includeSourceDetails
              ? `education[${index}].courses`
              : undefined,
          }))
        );
      }
    }

    return skills;
  }
}
