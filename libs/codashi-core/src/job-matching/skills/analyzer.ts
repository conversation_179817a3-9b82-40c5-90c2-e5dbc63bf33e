import type { BaseChatModel } from '@langchain/core/language_models/chat_models';

import { isNonEmptyArray } from '@awe/core';
import type { Job } from '../../entities/job';
import type { Resume } from '../../entities/resume';
import { DirectMatcher } from './direct-matcher';
import { SkillExtractor } from './extractor';
import { TransferableSkillExtractor } from './transferable-skills/transferable-skill-extractor';
import type {
  DirectSkillMatch,
  MissingSkill,
  SkillAnalysis,
  SkillAnalysisOptions,
  TransferableSkillMatch,
} from './types';
import { SkillMatchError } from './types';

/**
 * Analyzes skill matches between a single resume and a job posting.
 *
 * This class processes exactly one resume against one job description,
 * extracting skills directly from the resume and performing direct/transferable
 * matching against job requirements without any consolidation logic.
 */
export class SkillAnalyzer {
  private model: BaseChatModel;

  constructor(model: BaseChatModel) {
    this.model = model;
  }

  /**
   * Analyzes skill matches between a single resume and a job posting.
   *
   * @param resume - Single resume to analyze
   * @param job - Job posting to match against
   * @param options - Optional configuration for the analysis
   * @returns Promise resolving to comprehensive single-resume skill analysis
   *
   * @example
   * ```typescript
   * const analyzer = new SkillAnalyzer(mistralModel);
   * const analysis = await analyzer.analyzeSkills(
   *   resume,
   *   jobPosting,
   *   { includeSourceDetails: true, confidenceThreshold: 2 }
   * );
   *
   * console.log(`Direct matches: ${analysis.directMatches.length}`);
   * console.log(`Coverage: ${analysis.summary.coveragePercentage}%`);
   * ```
   */
  async analyzeSkills(
    resume: Resume,
    job: Job,
    options: SkillAnalysisOptions = {}
  ): Promise<SkillAnalysis> {
    try {
      const finalOptions = {
        maxTransferableSkills: 20,
        confidenceThreshold: 1,
        timeoutMs: 30000, // 30 seconds default timeout
        includeSourceDetails: false,
        ...options,
      } satisfies SkillAnalysisOptions;

      // Step 1: Extract skills from the single resume
      const skillExtractor = new SkillExtractor(this.model);
      const resumeSkills = await skillExtractor.extractSkills(
        resume,
        finalOptions
      );

      // Step 2: Find direct matches (exact, synonym, keyword)
      const directMatcher = new DirectMatcher(this.model);
      const directMatches = job.skills
        ? await directMatcher.findDirectMatches(
            resumeSkills,
            job.skills,
            finalOptions
          )
        : [];

      // Step 3: Identify unmatched skills for transferable analysis
      const { unmatchedResumeSkills, unmatchedJobSkills } =
        directMatcher.getUnmatchedSkills(
          resumeSkills,
          job.skills ?? [],
          directMatches
        );

      // Step 4: Analyze transferable skills using AI
      const transferableAnalyzer = new TransferableSkillExtractor(this.model);

      const transferableSkills =
        isNonEmptyArray(unmatchedResumeSkills) &&
        isNonEmptyArray(unmatchedJobSkills)
          ? await transferableAnalyzer.analyzeTransferableSkills(
              unmatchedResumeSkills,
              unmatchedJobSkills,
              finalOptions
            )
          : [];

      // Step 5: Identify missing skills
      const missingSkills = this.identifyMissingSkills(
        job.skills,
        directMatches,
        transferableSkills
      );

      // Step 6: Calculate summary statistics
      const summary = this.calculateSummary(
        resumeSkills,
        job.skills,
        directMatches,
        transferableSkills,
        missingSkills
      );

      return {
        resumeSkills,
        directMatches,
        transferableSkills,
        missingSkills,
        summary,
      };
    } catch (error) {
      throw new SkillMatchError(
        `Failed to analyze single resume skills: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        error
      );
    }
  }

  /**
   * Identifies missing skills that are required by the job but not covered
   * by direct matches or transferable skills from the resume.
   *
   * @param jobSkills - Skills required by the job posting
   * @param directMatches - Direct skill matches found
   * @param transferableSkills - Transferable skill matches found
   * @returns Array of missing skills
   */
  private identifyMissingSkills(
    jobSkills: Job['skills'],
    directMatches: DirectSkillMatch[],
    transferableSkills: TransferableSkillMatch[]
  ): MissingSkill[] {
    if (!jobSkills || jobSkills.length === 0) {
      return [];
    }

    // Create sets of matched job skills for efficient lookup
    const directlyMatchedSkills = new Set(
      directMatches.map((match) => match.jobSkill.toLowerCase())
    );
    const transferablyMatchedSkills = new Set(
      transferableSkills.map((match) => match.jobSkill.toLowerCase())
    );

    // Find job skills that are not covered by any matches
    const missingSkills: MissingSkill[] = [];

    for (const jobSkill of jobSkills) {
      const skillNameLower = jobSkill.name.toLowerCase();

      if (
        !directlyMatchedSkills.has(skillNameLower) &&
        !transferablyMatchedSkills.has(skillNameLower)
      ) {
        missingSkills.push({
          name: jobSkill.name,
          level: jobSkill.level,
          keywords: jobSkill.keywords,
          category: undefined, // Could be enhanced with categorization logic
        });
      }
    }

    return missingSkills;
  }

  /**
   * Calculates summary statistics for the skill analysis
   */
  private calculateSummary(
    resumeSkills: SkillAnalysis['resumeSkills'],
    jobSkills: Job['skills'],
    directMatches: SkillAnalysis['directMatches'],
    transferableSkills: SkillAnalysis['transferableSkills'],
    missingSkills: SkillAnalysis['missingSkills']
  ): SkillAnalysis['summary'] {
    const totalJobSkills = jobSkills?.length || 0;
    const totalResumeSkills = resumeSkills.length;
    const directMatchCount = directMatches.length;
    const transferableMatchCount = transferableSkills.length;
    const missingSkillCount = missingSkills.length;

    const totalMatches = directMatchCount + transferableMatchCount;
    const coveragePercentage =
      totalJobSkills > 0
        ? Math.round((totalMatches / totalJobSkills) * 100)
        : 0;

    return {
      totalJobSkills,
      totalResumeSkills,
      directMatchCount,
      transferableMatchCount,
      missingSkillCount,
      coveragePercentage,
    };
  }
}
