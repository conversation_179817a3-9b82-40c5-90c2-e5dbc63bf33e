/**
 * Modular prompt components for experience analysis
 *
 * This module provides reusable prompt sections that can be:
 * 1. Used independently for single-section LLM calls
 * 2. Combined together for multi-section extraction in a single LLM call
 */

import type { ResumeEntity } from '../../entities/resume';

/**
 * Job context section - provides job information for analysis
 */
const JOB_CONTEXT_SECTION = `
Job Title: {jobTitle}
Job Description: {jobDescription}
Required Skills: {jobSkills}
Required Qualifications: {jobQualifications}
`;

/**
 * Experience context section - provides work experience information
 */
const EXPERIENCE_CONTEXT_SECTION = `
Work Experience to Analyze:
{experienceList}
`;

/**
 * Scoring criteria section - defines how to evaluate experience relevance
 */
const SCORING_CRITERIA_SECTION = `
Consider these factors in your scoring:
- Role/position similarity to the target job
- Industry relevance and transferable skills
- Seniority level alignment
- Specific accomplishments that demonstrate relevant capabilities
- Technologies, tools, and methodologies mentioned
- Leadership experience and team management (if relevant to target role)
- Problem-solving examples and measurable impact
- Career progression and growth trajectory

Scoring Scale:
5 = Highly relevant - Direct match with strong transferable skills and accomplishments
4 = Very relevant - Good alignment with clear value proposition
3 = Moderately relevant - Some transferable skills but may need positioning
2 = Somewhat relevant - Limited alignment, requires significant positioning
1 = Minimally relevant - Little to no clear connection to target role
`;

/**
 * Full standalone prompt for experience scoring
 */
export const EXPERIENCE_SCORING_STANDALONE_PROMPT = `
You are an expert career advisor analyzing work experience relevance for job applications.

${JOB_CONTEXT_SECTION}

${EXPERIENCE_CONTEXT_SECTION}

${SCORING_CRITERIA_SECTION}

Please score each work experience on a scale of 1-5 based on its relevance to the target job.
Provide specific reasoning for each score, focusing on transferable skills and relevant accomplishments.

{format_instructions}
`;

/**
 * Helper function to format work experience for prompts
 */
export const formatExperienceForPrompt = (
  experience: ResumeEntity<'work'> & { id?: string },
  index?: number
): string => {
  const prefix = index !== undefined ? `Experience ${index + 1}` : 'Experience';
  const id = experience.id ? ` (ID: ${experience.id})` : '';

  let formatted = `${prefix}${id}:
Company: ${experience.name || 'Not specified'}
Position: ${experience.position || 'Not specified'}
Duration: ${experience.start_date} to ${experience.end_date || 'Present'}`;

  if (experience.location) {
    formatted += `\nLocation: ${experience.location}`;
  }

  if (experience.summary) {
    formatted += `\nSummary: ${experience.summary}`;
  }

  if (experience.description) {
    formatted += `\nDescription: ${experience.description}`;
  }

  if (experience.highlights && experience.highlights.length > 0) {
    formatted += `\nKey Highlights:\n${experience.highlights
      .map((h: string) => `• ${h}`)
      .join('\n')}`;
  }

  if (experience.positions && experience.positions.length > 0) {
    formatted += `\nPositions within company:\n${experience.positions
      .map(
        (pos, i: number) =>
          `  ${i + 1}. ${pos.position} (${pos.start_date} to ${
            pos.end_date || 'Present'
          })`
      )
      .join('\n')}`;
  }

  return formatted;
};
