import { z } from 'zod';

import { ResumeEntity } from '../../entities/resume';

/**
 * Represents a work experience with its relevance score from single resume analysis
 */
export type WorkExperienceScore = {
  /** The work experience being scored */
  experience: WorkExperience;
  /** Relevance score from 1-5 (5 being most relevant) */
  relevanceScore: number;
  /** AI reasoning for the assigned score */
  reasoning: string;
};

/**
 * Configuration options for single resume experience analysis
 */
export type ExperienceAnalysisOptions = {
  /** Timeout in milliseconds for AI operations */
  timeoutMs?: number;
  /** Batch size for LLM calls (number of experiences per call) */
  batchSize?: number;
};

/**
 * Complete analysis result for single resume work experience matching
 */
export type ExperienceAnalysis = {
  /** All work experiences from the single resume */
  experiences: WorkExperience[];
  /** Work experiences with relevance scores */
  scoredExperiences: WorkExperienceScore[];
  /** Summary statistics */
  summary: {
    /** Total number of work experiences */
    totalExperiences: number;
    /** Average relevance score across all experiences */
    averageRelevanceScore: number;
    /** Number of highly relevant experiences (score >= 4) */
    highRelevanceCount: number;
  };
};

/**
 * Schema for AI-powered single resume experience scoring results
 */
export const experienceScoreSchema = z.object({
  scores: z.array(
    z.object({
      experienceId: z.string(),
      relevanceScore: z.number().min(1).max(5),
      reasoning: z.string().max(300),
    })
  ),
});

export type ExperienceScoreResult = z.infer<typeof experienceScoreSchema>;

/**
 * Represents a work experience from a single resume (no consolidation)
 */
export type WorkExperience = ResumeEntity<'work'> & {
  // artificially generated
  id: string;
};
