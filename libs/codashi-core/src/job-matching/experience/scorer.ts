import type { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { StructuredOutputParser } from '@langchain/core/output_parsers';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import { RunnableSequence } from '@langchain/core/runnables';

import type { Job } from '../../entities/job';
import {
  isAIUnavailableError,
  isTimeoutError,
  withTimeout,
} from '../../utils/common-utils';
import { formatJobContext } from '../utils';
import {
  EXPERIENCE_SCORING_STANDALONE_PROMPT,
  formatExperienceForPrompt,
} from './prompt-components';
import type {
  ExperienceAnalysisOptions,
  ExperienceScoreResult,
  WorkExperience,
  WorkExperienceScore,
} from './types';
import { experienceScoreSchema } from './types';

/**
 * Scores work experiences from a single resume against job requirements.
 *
 * This class uses AI to evaluate how relevant each work experience is
 * to the target job, providing scores and reasoning without any consolidation logic.
 */
export class ExperienceScorer {
  private readonly DEFAULT_BATCH_SIZE = 8;
  private readonly MAX_SAFE_TOKENS = 8000;

  constructor(private model: BaseChatModel) {}

  /**
   * Scores work experiences from a single resume against job requirements.
   *
   * @param experiences - Array of work experiences from single resume to score
   * @param job - Job posting to score against
   * @param options - Configuration options
   * @returns Promise resolving to array of scored experiences
   */
  async scoreExperiences(
    experiences: WorkExperience[],
    job: Job,
    options: ExperienceAnalysisOptions = {}
  ): Promise<WorkExperienceScore[]> {
    if (!experiences.length) {
      return [];
    }

    const batchSize = options.batchSize || this.DEFAULT_BATCH_SIZE;
    const timeoutMs = options.timeoutMs || 30000;

    try {
      // Determine if we should use batching
      const totalTokens = this.estimateTotalTokens(experiences, job);

      if (
        totalTokens <= this.MAX_SAFE_TOKENS &&
        experiences.length <= batchSize
      ) {
        // Process all experiences in a single call
        return await this.scoreBatch(experiences, job, timeoutMs);
      } else {
        // Process in batches
        return await this.scoreInBatches(
          experiences,
          job,
          batchSize,
          timeoutMs
        );
      }
    } catch (error) {
      if (isAIUnavailableError(error) || isTimeoutError(error)) {
        // Return default scores if AI is unavailable
        return this.createDefaultScores(experiences);
      }
      throw error;
    }
  }

  /**
   * Scores a batch of experiences in a single LLM call
   */
  private async scoreBatch(
    experiences: WorkExperience[],
    job: Job,
    timeoutMs: number
  ): Promise<WorkExperienceScore[]> {
    const prompt = ChatPromptTemplate.fromTemplate(
      EXPERIENCE_SCORING_STANDALONE_PROMPT
    );
    const parser = StructuredOutputParser.fromZodSchema(experienceScoreSchema);
    const chain = RunnableSequence.from([prompt, this.model, parser]);

    const experienceList = experiences
      .map((exp, index) => formatExperienceForPrompt(exp, index))
      .join('\n\n');

    const jobContext = formatJobContext(job);

    const result = await withTimeout(
      chain.invoke({
        ...jobContext,
        experienceList,
        format_instructions: parser.getFormatInstructions(),
      }),
      timeoutMs,
      'Single resume experience scoring'
    );

    return this.mapResultsToScores(result, experiences);
  }

  /**
   * Scores experiences in batches
   */
  private async scoreInBatches(
    experiences: WorkExperience[],
    job: Job,
    batchSize: number,
    timeoutMs: number
  ): Promise<WorkExperienceScore[]> {
    const allScores: WorkExperienceScore[] = [];

    for (let i = 0; i < experiences.length; i += batchSize) {
      const batch = experiences.slice(i, i + batchSize);
      const batchScores = await this.scoreBatch(batch, job, timeoutMs);
      allScores.push(...batchScores);
    }

    return allScores;
  }

  /**
   * Maps AI results to scored experiences
   */
  private mapResultsToScores(
    result: ExperienceScoreResult,
    experiences: WorkExperience[]
  ): WorkExperienceScore[] {
    const scores: WorkExperienceScore[] = [];

    for (const scoreResult of result.scores) {
      const experience = experiences.find(
        (exp) => exp.id === scoreResult.experienceId
      );

      if (experience) {
        scores.push({
          experience,
          relevanceScore: scoreResult.relevanceScore,
          reasoning: scoreResult.reasoning,
        });
      }
    }

    // Add default scores for any experiences not found in results
    for (const experience of experiences) {
      if (!scores.find((score) => score.experience.id === experience.id)) {
        scores.push({
          experience,
          relevanceScore: 3, // Default neutral score
          reasoning: 'Unable to analyze - using default score',
        });
      }
    }

    return scores;
  }

  /**
   * Creates default scores when AI is unavailable
   */
  private createDefaultScores(
    experiences: WorkExperience[]
  ): WorkExperienceScore[] {
    return experiences.map((experience) => ({
      experience,
      relevanceScore: 3, // Default neutral score
      reasoning: 'AI analysis unavailable - using default score',
    }));
  }

  /**
   * Estimates total token count for experiences and job
   */
  private estimateTotalTokens(experiences: WorkExperience[], job: Job): number {
    let tokenCount = 0;

    // Estimate job tokens
    tokenCount += (job.title?.length || 0) / 4;
    tokenCount += (job.description?.length || 0) / 4;

    if (job.skills) {
      tokenCount += job.skills.reduce(
        (sum, skill) => sum + (skill.name?.length || 0) / 4,
        0
      );
    }

    // Estimate experience tokens
    for (const exp of experiences) {
      tokenCount += (exp.name?.length || 0) / 4;
      tokenCount += (exp.position?.length || 0) / 4;
      tokenCount += (exp.summary?.length || 0) / 4;
      tokenCount += (exp.description?.length || 0) / 4;

      if (exp.highlights) {
        tokenCount += exp.highlights.join(' ').length / 4;
      }
    }

    return Math.ceil(tokenCount);
  }
}
