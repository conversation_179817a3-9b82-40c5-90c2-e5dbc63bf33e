import type { Resume } from '../../entities/resume';
import type { WorkExperience } from './types';

/**
 * Extracts work experiences from a single resume without any consolidation logic.
 *
 * This class replaces the complex consolidation logic with simple extraction,
 * processing exactly one resume to extract its work experiences.
 */
export class ExperienceExtractor {
  /**
   * Extracts work experiences from a single resume.
   *
   * @param resume - Single resume to extract experiences from
   * @returns Array of work experiences from the resume
   */
  extractExperiences(resume: Resume): WorkExperience[] {
    const experiences: WorkExperience[] = [];

    // Find the work section in the resume
    const workSection = resume.sections.find(
      (section) => section.name === 'work'
    );

    if (!workSection || !workSection.items) {
      return experiences;
    }

    // Extract each work experience directly without consolidation
    for (const workItem of workSection.items) {
      const experience: WorkExperience = {
        id: this.generateExperienceId(
          workItem.name,
          workItem.position,
          workItem.start_date
        ),
        name: workItem.name ?? '',
        position: workItem.position ?? '',
        location: workItem.location || null,
        start_date: workItem.start_date,
        end_date: workItem.end_date || null,
        summary: workItem.summary || null,
        highlights: workItem.highlights || null,
        description: workItem.description || null,
        url: workItem.url || null,
        positions: workItem.positions,
      };

      experiences.push(experience);
    }

    // Sort by start date, most recent first
    return experiences.sort((a, b) => {
      return (
        new Date(b.start_date).getTime() - new Date(a.start_date).getTime()
      );
    });
  }

  /**
   * Generates a unique ID for a work experience
   */
  private generateExperienceId(
    company: string | undefined,
    position: string | undefined,
    startDate: string | undefined
  ): string {
    const normalized = `${this.normalizeCompanyName(
      company
    )}-${this.normalizePositionTitle(position)}-${startDate || 'unknown'}`;
    return normalized.replace(/[^\w-]/g, '').substring(0, 50);
  }

  /**
   * Normalizes company names for ID generation
   */
  private normalizeCompanyName(name: string | undefined): string {
    if (!name) return '';
    return name
      .toLowerCase()
      .trim()
      .replace(/\b(inc|llc|ltd|corp|corporation|company|co)\b\.?/g, '')
      .replace(/[^\w\s]/g, '')
      .replace(/\s+/g, ' ')
      .trim();
  }

  /**
   * Normalizes position titles for ID generation
   */
  private normalizePositionTitle(title: string | undefined): string {
    if (!title) return '';
    return title
      .toLowerCase()
      .trim()
      .replace(/[^\w\s]/g, '')
      .replace(/\s+/g, ' ')
      .trim();
  }

  /**
   * Estimates token count for a work experience (rough approximation)
   */
  estimateExperienceTokens(experience: WorkExperience): number {
    let tokenCount = 0;

    tokenCount += (experience.name?.length || 0) / 4;
    tokenCount += (experience.position?.length || 0) / 4;
    tokenCount += (experience.summary?.length || 0) / 4;
    tokenCount += (experience.description?.length || 0) / 4;

    if (experience.highlights) {
      tokenCount += experience.highlights.join(' ').length / 4;
    }

    return Math.ceil(tokenCount);
  }

  /**
   * Estimates total token count for multiple experiences
   */
  estimateTotalTokens(experiences: WorkExperience[]): number {
    return experiences.reduce(
      (total, exp) => total + this.estimateExperienceTokens(exp),
      0
    );
  }
}
