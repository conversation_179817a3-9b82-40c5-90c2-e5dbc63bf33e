import type { BaseChatModel } from '@langchain/core/language_models/chat_models';

import { isNonEmptyArray } from '@awe/core';
import type { Job } from '../../entities/job';
import type { Resume } from '../../entities/resume';
import {
  isAIUnavailableError,
  isTimeoutError,
  withTimeout,
} from '../../utils/common-utils';
import { CertificationExtractor } from './extractor';
import { CertificationScorer } from './scorer';
import type {
  CertificationAnalysis,
  CertificationAnalysisOptions,
} from './types';

/**
 * Analyzes certifications relevance for a single resume against a job posting.
 */
export class CertificationAnalyzer {
  private readonly model: BaseChatModel;

  constructor(model: BaseChatModel) {
    this.model = model;
  }

  async analyzeCertifications(
    resume: Resume,
    job: Job,
    options: CertificationAnalysisOptions = {}
  ): Promise<CertificationAnalysis> {
    const finalOptions = {
      timeoutMs: 30000,
      batchSize: 12,
      ...options,
    } satisfies CertificationAnalysisOptions;

    try {
      const extractor = new CertificationExtractor();
      const certifications = extractor.extractCertifications(resume);

      if (!isNonEmptyArray(certifications)) {
        return {
          certifications: [],
          scoredCertifications: [],
          summary: {
            totalCertifications: 0,
            averageRelevanceScore: 0,
            highRelevanceCount: 0,
          },
        };
      }

      const scorer = new CertificationScorer(this.model);
      const scoredCertifications = await withTimeout(
        scorer.scoreCertifications(certifications, job, finalOptions),
        finalOptions.timeoutMs,
        'Single resume certifications scoring'
      );

      const summary = this.calculateSummary(scoredCertifications);

      return {
        certifications,
        scoredCertifications,
        summary,
      };
    } catch (error) {
      if (isAIUnavailableError(error)) {
        throw new CertificationMatchError(
          'AI model is currently unavailable for certifications analysis',
          'AI_UNAVAILABLE'
        );
      }

      if (isTimeoutError(error)) {
        throw new CertificationMatchError(
          'Certifications analysis timed out',
          'TIMEOUT'
        );
      }

      throw new CertificationMatchError(
        `Certifications analysis failed: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`,
        'INVALID_INPUT'
      );
    }
  }

  private calculateSummary(
    scored: CertificationAnalysis['scoredCertifications']
  ): CertificationAnalysis['summary'] {
    const total = scored.length;

    if (total === 0) {
      return {
        totalCertifications: 0,
        averageRelevanceScore: 0,
        highRelevanceCount: 0,
      };
    }

    const totalScore = scored.reduce((sum, s) => sum + s.relevanceScore, 0);
    const averageRelevanceScore = Math.round((totalScore / total) * 10) / 10;
    const highRelevanceCount = scored.filter(
      (s) => s.relevanceScore >= 4
    ).length;

    return {
      totalCertifications: total,
      averageRelevanceScore,
      highRelevanceCount,
    };
  }
}

class CertificationMatchError extends Error {
  constructor(
    message: string,
    public readonly code:
      | 'AI_UNAVAILABLE'
      | 'TIMEOUT'
      | 'INVALID_INPUT'
      | 'NO_CERTIFICATIONS'
  ) {
    super(message);
    this.name = 'CertificationMatchError';
  }
}
