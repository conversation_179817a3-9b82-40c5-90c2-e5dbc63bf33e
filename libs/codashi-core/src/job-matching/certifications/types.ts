import { z } from 'zod';

/**
 * Represents a certification item extracted from a single resume
 */
export type CertificationItem = {
  // artificially generated stable id
  id: string;
  name: string | null;
  issuer: string | null;
  date: string | null;
  url: string | null;
};

/**
 * Configuration options for single resume certifications analysis
 */
export type CertificationAnalysisOptions = {
  /** Timeout in milliseconds for AI operations */
  timeoutMs?: number;
  /** Batch size for LLM calls (number of certifications per call) */
  batchSize?: number;
};

/**
 * Complete analysis result for single resume certifications matching
 */
export type CertificationAnalysis = {
  /** All certifications from the single resume */
  certifications: CertificationItem[];
  /** Certifications with relevance scores */
  scoredCertifications: CertificationScore[];
  /** Summary statistics */
  summary: {
    /** Total number of certifications */
    totalCertifications: number;
    /** Average relevance score across all certifications */
    averageRelevanceScore: number;
    /** Number of highly relevant certifications (score >= 4) */
    highRelevanceCount: number;
  };
};

/**
 * Schema for AI-powered single resume certifications scoring results
 */
export const certificationScoreSchema = z.object({
  scores: z.array(
    z.object({
      certificationId: z.string(),
      relevanceScore: z.number().min(1).max(5),
      reasoning: z.string().max(300),
    })
  ),
});

export type CertificationScoreResult = z.infer<typeof certificationScoreSchema>;

export type CertificationScore = z.infer<
  typeof certificationScoreSchema
>['scores'][number];
