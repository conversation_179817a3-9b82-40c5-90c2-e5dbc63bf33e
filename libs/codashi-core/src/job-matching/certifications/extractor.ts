import type { Resume } from '../../entities/resume';
import type { CertificationItem } from './types';

/**
 * Extracts certifications from a single resume without consolidation.
 */
export class CertificationExtractor {
  extractCertifications(resume: Resume): CertificationItem[] {
    const certifications: CertificationItem[] = [];

    const certsSection = resume.sections.find((s) => s.name === 'certificates');
    if (!certsSection || !certsSection.items) return certifications;

    for (const c of certsSection.items) {
      const cert: CertificationItem = {
        id: this.generateCertificationId(c.name, c.issuer, c.date),
        name: c.name ?? null,
        issuer: c.issuer ?? null,
        date: c.date ?? null,
        url: c.url ?? null,
      };
      certifications.push(cert);
    }

    // Sort by date, most recent first if dates exist
    return certifications.sort((a, b) => {
      const da = a.date ? new Date(a.date).getTime() : 0;
      const db = b.date ? new Date(b.date).getTime() : 0;
      return db - da;
    });
  }

  private generateCertificationId(
    name?: string | null,
    issuer?: string | null,
    date?: string | null
  ): string {
    const normalized = `${this.normalize(name)}-${this.normalize(issuer)}-${
      date || 'unknown'
    }`;
    return normalized.replace(/[^\w-]/g, '').substring(0, 50);
  }

  private normalize(v?: string | null): string {
    if (!v) return '';
    return v
      .toLowerCase()
      .trim()
      .replace(/[^\w\s]/g, '')
      .replace(/\s+/g, ' ');
  }
}
