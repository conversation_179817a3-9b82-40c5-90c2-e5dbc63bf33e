/**
 * Modular prompt components for certifications analysis
 */

import type { CertificationItem } from './types';

/**
 * Certifications context section - provides certifications information
 */
const CERTIFICATIONS_CONTEXT_SECTION = `
Certifications to Analyze:
{certificationList}
`;

/**
 * Scoring criteria for certifications relevance
 */
const CERTIFICATIONS_SCORING_CRITERIA = `
Consider these factors in your scoring:
- Alignment to required/desired skills and qualifications in the job
- Recognition and credibility of the issuing organization
- Recency (newer certifications may be more relevant for rapidly evolving fields)
- Level/rigor of the certification (foundational vs professional/expert)
- Direct applicability to the role's responsibilities or domain

Scoring Scale:
5 = Highly relevant - Directly supports core requirements of the role
4 = Very relevant - Strongly beneficial for the role
3 = Moderately relevant - Provides useful but indirect value
2 = Somewhat relevant - Limited applicability
1 = Minimally relevant - Little to no impact for the role
`;

/**
 * Full standalone prompt for certifications scoring
 */
export const CERTIFICATION_SCORING_STANDALONE_PROMPT = `
You are an expert career advisor analyzing how relevant a candidate's certifications are for a target job.

Job Context:
Job Title: {jobTitle}
Job Description: {jobDescription}
Required Skills: {jobSkills}
Required Qualifications: {jobQualifications}

${CERTIFICATIONS_CONTEXT_SECTION}

${CERTIFICATIONS_SCORING_CRITERIA}

Please score each certification on a scale of 1-5 and provide concise reasoning.

{format_instructions}
`;

/**
 * Helper to format certifications for prompt
 */
export const formatCertificationForPrompt = (
  cert: CertificationItem,
  index?: number
): string => {
  const prefix =
    index !== undefined ? `Certification ${index + 1}` : 'Certification';
  const id = cert.id ? ` (ID: ${cert.id})` : '';

  let formatted = `${prefix}${id}:
Name: ${cert.name || 'Not specified'}
Issuer: ${cert.issuer || 'Not specified'}
Date: ${cert.date || 'Not specified'}`;

  if (cert.url) {
    formatted += `\nURL: ${cert.url}`;
  }

  return formatted;
};
