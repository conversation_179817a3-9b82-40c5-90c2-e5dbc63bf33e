import type { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { StructuredOutputParser } from '@langchain/core/output_parsers';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import { RunnableSequence } from '@langchain/core/runnables';

import type { Job } from '../../entities/job';
import { withTimeout } from '../../utils/common-utils';
import { formatJobContext } from '../utils';
import {
  CERTIFICATION_SCORING_STANDALONE_PROMPT,
  formatCertificationForPrompt,
} from './prompt-components';
import type {
  CertificationAnalysisOptions,
  CertificationItem,
  CertificationScore,
  CertificationScoreResult,
} from './types';
import { certificationScoreSchema } from './types';

/**
 * Scores certifications from a single resume against job requirements.
 */
export class CertificationScorer {
  private readonly DEFAULT_BATCH_SIZE = 12;
  private readonly MAX_SAFE_TOKENS = 8000;

  constructor(private model: BaseChatModel) {}

  async scoreCertifications(
    certs: CertificationItem[],
    job: Job,
    options: CertificationAnalysisOptions = {}
  ): Promise<CertificationScore[]> {
    const batchSize = options.batchSize || this.DEFAULT_BATCH_SIZE;
    const timeoutMs = options.timeoutMs || 30000;

    const totalTokens = this.estimateTotalTokens(certs, job);
    if (totalTokens <= this.MAX_SAFE_TOKENS && certs.length <= batchSize) {
      return await this.scoreBatch(certs, job, timeoutMs);
    } else {
      return await this.scoreInBatches(certs, job, batchSize, timeoutMs);
    }
  }

  private async scoreBatch(
    certs: CertificationItem[],
    job: Job,
    timeoutMs: number
  ): Promise<CertificationScore[]> {
    const prompt = ChatPromptTemplate.fromTemplate(
      CERTIFICATION_SCORING_STANDALONE_PROMPT
    );
    const parser = StructuredOutputParser.fromZodSchema(
      certificationScoreSchema
    );
    const chain = RunnableSequence.from([prompt, this.model, parser]);

    const certificationList = certs
      .map((c, index) => formatCertificationForPrompt(c, index))
      .join('\n\n');

    const jobContext = formatJobContext(job);

    const result = await withTimeout(
      chain.invoke({
        ...jobContext,
        certificationList,
        format_instructions: parser.getFormatInstructions(),
      }),
      timeoutMs,
      'Single resume certifications scoring'
    );

    return this.mapResultsToScores(result, certs);
  }

  private async scoreInBatches(
    certs: CertificationItem[],
    job: Job,
    batchSize: number,
    timeoutMs: number
  ): Promise<CertificationScore[]> {
    const allScores: CertificationScore[] = [];
    const failedBatches: CertificationItem[][] = [];

    for (let i = 0; i < certs.length; i += batchSize) {
      const batch = certs.slice(i, i + batchSize);
      try {
        const batchScores = await this.scoreBatch(batch, job, timeoutMs);
        allScores.push(...batchScores);
      } catch (error) {
        console.error(
          `Failed to score certifications batch starting at index ${i}:`,
          error
        );
        failedBatches.push(batch);
      }
    }

    if (failedBatches.length > 0) {
      console.warn(
        `Failed to score ${failedBatches.length} certification batches`
      );
    }

    return allScores;
  }

  private mapResultsToScores(
    result: CertificationScoreResult,
    certs: CertificationItem[]
  ): CertificationScore[] {
    const scores: CertificationScore[] = [];
    for (const scoreResult of result.scores) {
      const cert = certs.find((c) => c.id === scoreResult.certificationId);
      if (cert) {
        scores.push({
          certificationId: scoreResult.certificationId,
          relevanceScore: scoreResult.relevanceScore,
          reasoning: scoreResult.reasoning,
        });
      }
    }
    return scores;
  }

  private estimateTotalTokens(certs: CertificationItem[], job: Job): number {
    let tokenCount = 0;
    tokenCount += (job.title?.length || 0) / 4;
    tokenCount += (job.description?.length || 0) / 4;

    if (job.skills) {
      tokenCount += job.skills.reduce(
        (sum, skill) => sum + (skill.name?.length || 0) / 4,
        0
      );
    }

    for (const c of certs) {
      tokenCount += (c.name?.length || 0) / 4;
      tokenCount += (c.issuer?.length || 0) / 4;
      tokenCount += (c.date?.length || 0) / 4;
    }

    return Math.ceil(tokenCount);
  }
}
