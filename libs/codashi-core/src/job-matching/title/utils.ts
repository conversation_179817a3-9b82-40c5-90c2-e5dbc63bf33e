import z from 'zod';

/**
 * Represents a single resume title analysis result.
 * This is the result of analyzing one resume against one job posting for title matching.
 */
export type TitleAnalysis = {
  /** The original title from the resume */
  originalTitle: string;
  /** The job title being matched against */
  jobTitle: string;
  /** Confidence score for the title match (0-1) */
  confidence: number;
  /** AI reasoning for the title match */
  reasoning: string;
  /** Suggested improved title (if any) */
  suggestedTitle?: string;
  /** Summary statistics for the title analysis */
  summary: {
    /** Whether the title is a good match for the job */
    isGoodMatch: boolean;
    /** Confidence level: 'low', 'medium', 'high' */
    confidenceLevel: 'low' | 'medium' | 'high';
    /** Whether an improvement was suggested */
    hasImprovement: boolean;
  };
};

/**
 * Options for configuring single-resume title analysis
 */
export type TitleAnalysisOptions = {
  /** Timeout in milliseconds for AI operations */
  timeoutMs?: number;
  /** Whether to suggest title improvements */
  suggestImprovements?: boolean;
  /** Minimum confidence threshold (0-1) */
  confidenceThreshold?: number;
};

/**
 * Error class for single-resume title matching operations
 */
export class TitleMatchError extends Error {
  constructor(message: string, public readonly cause?: unknown) {
    super(message);
    this.name = 'TitleMatchError';
  }
}

/**
 * Zod schema for title analysis AI response
 */
export const titleAnalysisSchema = z.object({
  confidence: z
    .number()
    .min(0)
    .max(1)
    .describe('Confidence score for the title match (0-1)'),
  reasoning: z
    .string()
    .describe('Detailed reasoning for the title match assessment'),
  isGoodMatch: z
    .boolean()
    .describe('Whether the title is a good match for the job'),
  suggestedTitle: z
    .string()
    .optional()
    .describe('Suggested improved title if improvements are needed'),
});

/**
 * Type for the AI response schema
 */
export type TitleAnalysisResponse = z.infer<typeof titleAnalysisSchema>;
