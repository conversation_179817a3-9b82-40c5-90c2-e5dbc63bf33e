import { z } from 'zod';

import { dateTimeParser } from './Date';

export type StrictOmit<T, K extends keyof T> = Omit<T, K> & {
  [P in K]-?: never;
};

export type StrictExtract<Type, Union extends Partial<Type>> = Extract<
  Type,
  Union
>;

export type DeepPartial<T> = {
  [P in keyof T]?: DeepPartial<T[P]>;
};

export const withVersioning = z.object({
  created_at: dateTimeParser,
  updated_at: dateTimeParser.optional(),
});

export type Pretify<T> = {
  [K in keyof T]: T[K];
  // eslint-disable-next-line @typescript-eslint/ban-types
} & {};

// vanilla Omit doesn't work well with union types
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type DistributedOmit<T, K extends keyof any> = T extends any
  ? Omit<T, K>
  : never;

export type DeepMutable<T> = {
  -readonly [P in keyof T]: T[P] extends object ? DeepMutable<T[P]> : T[P];
};

export type MakeNonNullable<T> = {
  [K in keyof T]-?: T[K] extends object ? MakeNonNullable<T[K]> : T[K];
};

export type NonEmptyArray<T> = [T, ...T[]];

export const isNonEmptyArray = <T>(value: T[]): value is NonEmptyArray<T> => {
  return value.length > 0;
};
