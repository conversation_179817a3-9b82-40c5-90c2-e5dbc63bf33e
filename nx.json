{"$schema": "./node_modules/nx/schemas/nx-schema.json", "defaultBase": "origin/main", "targetDefaults": {"build": {"cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production"]}, "@nx/vite:test": {"cache": true, "inputs": ["default", "^production"], "options": {"passWithNoTests": true}}, "@nx/eslint:lint": {"cache": true, "inputs": ["default", "{workspaceRoot}/.eslintrc.json", "{workspaceRoot}/.eslintignore", "{workspaceRoot}/eslint.config.js"]}, "e2e": {"cache": true, "inputs": ["default", "^production"]}, "astro": {"cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production"]}, "build-storybook": {"cache": true, "inputs": ["default", "^production", "{projectRoot}/.storybook/**/*", "{projectRoot}/tsconfig.storybook.json"]}}, "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/eslint.config.js", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/src/test-setup.[jt]s", "!{projectRoot}/**/*.stories.@(js|jsx|ts|tsx|mdx)", "!{projectRoot}/.storybook/**/*", "!{projectRoot}/tsconfig.storybook.json"], "sharedGlobals": []}, "generators": {"@nx/react": {"library": {"unitTestRunner": "vitest"}}}}