{"mcpServers": {"context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "env": {"DEFAULT_MINIMUM_TOKENS": ""}, "alwaysAllow": ["resolve-library-id", "get-library-docs"]}, "sequentialthinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "alwaysAllow": ["sequentialthinking"]}, "puppeteer": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"], "alwaysAllow": ["puppeteer_screenshot", "puppeteer_navigate", "puppeteer_click", "puppeteer_fill", "puppeteer_select", "puppeteer_hover", "puppeteer_evaluate"]}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "env": {"STORAGE_PATH": "ai-assets/data/memory.jsonl"}, "alwaysAllow": ["create_entities", "create_relations", "add_observations", "delete_entities", "delete_observations", "delete_relations", "read_graph", "search_nodes", "open_nodes"], "disabledTools": []}}}